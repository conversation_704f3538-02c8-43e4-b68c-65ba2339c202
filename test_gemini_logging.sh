#!/bin/bash

# 测试 Gemini 详细日志功能的脚本

echo "Testing Gemini detailed logging functionality..."

# 检查是否有 done-hub 进程在运行
if ! pgrep -f "done-hub" > /dev/null; then
    echo "Error: done-hub is not running. Please start the service first."
    exit 1
fi

# 设置测试参数
API_BASE="http://localhost:3000"
API_KEY="your-api-key-here"  # 请替换为实际的 API key

# 测试 Gemini API 请求
echo "Sending test request to Gemini API..."

curl -X POST "${API_BASE}/gemini/v1/models/gemini-pro:generateContent" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${API_KEY}" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Hello, this is a test message for logging"
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.7,
      "maxOutputTokens": 100
    }
  }' \
  -v

echo ""
echo "Test request sent. Check the logs for detailed information:"
echo "1. Check console output for [DEBUG] and [ERROR] messages"
echo "2. Check log files in ./logs/ directory"
echo "3. Look for request/response details in the logs"

echo ""
echo "Expected log entries:"
echo "- Gemini request details (URL, method, model action)"
echo "- Parsed request body (truncated if too long)"
echo "- Upstream request details (URL, headers, body)"
echo "- HTTP request/response information"
echo "- Detailed error information if request fails"
