{"systemInfo": "系統資訊", "CPU Usage": "CPU 使用率", "Memory Usage": "記憶體使用率", "System Logs": "系統日誌", "view_more_logs_on_server": "需要查看更多系統日誌請前往伺服器查看", "Auto Refresh": "自動重新整理", "Refresh Interval": "重新整理間隔", "1 second": "1 秒", "3 seconds": "3 秒", "5 seconds": "5 秒", "10 seconds": "10 秒", "30 seconds": "30 秒", "1 minute": "1 分鐘", "Max Entries": "最大條數", "Clear": "清空", "No logs available": "暫無日誌", "No matching logs found": "未找到匹配的日誌", "Search logs...": "搜尋日誌...", "invite_count": "邀請人數", "invite_reward": "邀請獎勵", "AZURE_OPENAI_ENDPOINT": "AZURE_OPENAI_ENDPOINT", "CheckUpdatesTable": {"addNewOnly": "僅添加新增", "cancel": "取消", "checkUpdates": "檢查更新", "dataFormatIncorrect": "數據格式不正確", "fetchData": "獲取數據", "inputMultiplierChanged": "輸入倍率由", "newModels": "新增模型", "noNewModels": "沒有新增模型", "noUpdates": "無更新", "note": "注意", "operationCompleted": "操作成功完成！", "outputMultiplierChanged": "輸出倍率由", "updateModeAdd": "只新增", "updateModeUpdate": "只更新", "updateModeOverwrite": "覆蓋所有", "overwriteOrAddOnly": "你可以選擇覆蓋或者僅添加新增，如果你選擇覆蓋，將會刪除你自己添加的模型價格，完全使用遠程配置，如果你選擇僅添加新增，將只會添加新增模型的價格", "pleaseFetchData": "請先獲取數據", "priceChangeModels": "價格變動模型（僅供參考，如果你自己修改了對應模型的價格請忽略）", "to": "變為", "url": "URL", "priceServerTotal": "價格服務器總數", "overwriteData": "", "added": "新增", "extraRatiosAdded": "新增擴展倍率", "extraRatiosChanged": "擴展倍率變化", "extraRatiosChanges": "擴展倍率變動", "extraRatiosChangesTip": "擴展倍率變動：{{changes}}", "extraRatiosRemoved": "刪除擴展倍率", "modified": "修改", "removed": "刪除"}, "Header 配置": "Header 配置", "sidebar": {"totalQuota": "總額度", "remainingBalance": "可用額度"}, "about": {"aboutDescription": "可在設置頁面設置關於內容，支持 HTML 和 Markdown", "aboutTitle": "關於", "loadingError": "加載關於內容失敗...", "projectRepo": "項目倉庫地址："}, "alloy 映射": "alloy 映射", "analytics": "分析", "plauground": "聊天", "analytics_index": {"active": "正常", "averageLatency": "平均延遲", "channelCount": "渠道數量", "consumptionStatistics": "消費統計", "directRegistration": "直接註冊", "disabled": "禁用", "endTime": "結束時間", "invitationRegistration": "邀請註冊", "redeemCodeIssued": "兌換碼發行量", "redemptionStatistics": "兌換統計", "registrationStatistics": "註冊統計", "requestsCount": "請求數", "startTime": "開始時間", "testDisabled": "測試禁用", "tokensStatistics": "Tokens統計", "totalUserBalance": "用戶總餘額", "totalUserSpending": "用戶總消費金額", "totalUsers": "用戶總數", "unused": "未使用", "used": "已使用", "rechargeStatistics": "充值統計", "redemptionCode": "兌換碼", "order": "訂單", "realTimeTraffic": "實時流量", "tpmDescription": "TPM (Token/分鐘)", "last60SecondsStats": "最近60秒統計"}, "auth": {"invalidLink": "無效的鏈接", "newPassword": "新密碼", "newPasswordEdit": "請登入後及時修改密碼", "newPasswordInfo": "你的新密碼是：", "restPassword": "密碼重置", "restPasswordClick": "點擊重置密碼"}, "channel": "渠道", "channel_edit": {"addModelHeader": "添加自定義Header", "addModelMapping": "添加模型映射", "addModelMappingByJson": "添加模型映射(JSON)", "addSuccess": "創建成功！", "batchAdd": "批量添加", "batchBaseurlTip": "，一行一個，順序對應下面的key，如果對應不上則默認使用第一個", "batchKeytip": "，一行一個密鑰", "customModelTip": "自定義：點擊或回車輸入", "editSuccess": "更新成功！", "inputAllModel": "填入所有模型", "inputChannelModel": "填入渠道支持模型", "invalidJson": "無效的JSON", "isEnable": "是否啟用", "jsonInputLabel": "JSON對象，key為請求模型，value為實際轉發模型", "modelHeaderKey": "自定義Header Key", "modelHeaderValue": "自定義Header Value", "modelListError": "獲取模型列表失敗", "modelMappingKey": "用戶請求模型", "modelMappingValue": "實際轉發模型", "requiredBaseUrl": "渠道API地址 不能為空", "requiredChannel": "渠道 不能為空", "requiredGroup": "用戶組 不能為空", "requiredKey": "密鑰 不能為空", "requiredModels": "模型 不能為空", "requiredName": "名稱 不能為空", "validJson": "必須是有效的JSON字符串", "collapse": "收起", "expand": "展開", "copyModels": "複製模型", "mapAdd": "新增{{name}}", "mapAddByJson": "透過 JSON 添加{{name}}", "mapJsonInput": "JSON 輸入", "listJsonError": "JSON格式錯誤，請確保輸入的是陣列格式。", "listJsonHelperText": "請輸入JSON數組格式，例如：[\"項目1\", \"項目2\", \"項目3\"]", "otherModels": "其他模型", "modelsFetched": "模型獲取成功", "selectedMappingCount": "已選{{count}}個模型映射", "addMapping": "添加模型映射", "addToModelMapping": "添加到模型映射", "modelMapping": "模型映射", "modelMappingSettings": "模型映射設置", "prefixOrSuffix": "前缀/後缀", "removePrefixHelp": "請輸入你要刪除的前缀", "removeSuffixHelp": "請輸入你要刪除的後缀", "addPlusSign": "使用映射前計費", "mappingPreview": "映射預覽", "removePrefix": "刪除前缀", "removeSuffix": "刪除後缀", "collapseList": "收起列表", "expandList": "展開列表"}, "channel_index": {"AzureApiVersion": "Azure 版本號", "actions": "操作", "all": "全部", "batchAzureAPISuccess": "成功更新 {{count}} 條數據", "batchDelete": "批量刪除模型", "batchDeleteModel": "請輸入完整模型名稱", "batchDeleteSuccess": "成功刪除 {{count}} 條數據", "batchDeleteTip": "如果渠道只有一個模型的，將不會顯示，請手動去列表刪除渠道", "batchProcessing": "批量處理", "channel": "渠道", "channelList": "渠道列表", "channelName": "渠道名稱", "channelTags": "渠道標籤", "channelType": "渠道類型", "deleteDisabledChannels": "刪除禁用渠道", "description1": "1. 優先級越大，越優先使用；(只有該優先級下的節點都凍結或者禁用了，才會使用低優先級的節點)", "description2": "2. 相同優先級下：根據權重進行負載均衡(加權隨機)", "description3": "3. 如果在設置-通用設置中設置了“重試次數”和“重試間隔”，則會在失敗後重試。", "description4": "4. 重試邏輯：1）先在高優先級中的節點重試，如果高優先級中的節點都凍結了，才會在低優先級中的節點重試。2）如果設置了“重試間隔”，則某一渠道失敗後，會凍結一段時間，所有人都不會再使用這個渠道，直到凍結時間結束。3）重試次數用完後，直接結束。", "disabled": "禁用", "enabled": "啟用", "filterTags": "過濾標籤", "group": "分組", "inputAPIVersion": "請輸入api版本號", "model": "模型", "modelName": "模型名稱", "name": "名稱", "newChannel": "新建渠道", "otherParameters": "其他參數", "priority": "優先級", "priorityWeightExplanation": "優先級/權重解釋：", "refreshClearSearchConditions": "刷新/清除搜索條件", "replaceValue": "替換值", "responseTime": "響應時間", "search": "搜索", "selectAll": "全選", "showAll": "顯示全部", "speedTestDisabled": "測速禁用", "status": "狀態", "supplier": "供應商", "tags": "標籤", "testAllChannels": "測試所有渠道", "testModel": "測試模型", "type": "類型", "unselectAll": "反全選", "updateEnabledBalance": "更新啟用餘額", "usedBalance": "已使用/餘額", "weight": "權重", "onlyTags": "僅顯示標籤"}, "channel_row": {"auto": "自動", "canModels": "可用模型：", "channelWeb": "官方網站", "clickUpdateQuota": "點擊更新餘額", "delChannel": "刪除渠道", "delChannelCount": "已刪除所有禁用渠道，共計 {{count}} 個", "delChannelInfo": "是否刪除渠道", "delTag": "刪除標籤", "delTagInfo1": "是否刪除標籤", "delTagInfo2": "⚠️ 注意：該操作會刪除渠道。", "manual": "手動", "modelTestSuccess": "通道 {{channel}}: {{model}} 測試成功，耗時 {{time}} 秒。", "modelTestTip": "請先設置測試模型", "onlyChat": "僅支持 chat 模型", "otherArg": "其他參數", "priorityTip": "優先級不能小於 0", "proxy": "代理地址：", "test": "測試", "testAllChannel": "已成功開始測試所有通道，請刷新頁面查看結果。", "testModels": "測速模型", "updateChannelBalance": "已更新完畢所有已啟用通道餘額！", "updateOk": "更新成功！", "weightTip": "權重不能小於 1", "check": "檢測", "batchAddIDRequired": "請至少選擇一個渠道", "batchDelete": "批量刪除", "batchDeleteConfirm": "確定要刪除選中的 {{count}} 個渠道嗎？此操作將無法復原。", "batchDeleteError": "批量刪除失敗: {{message}}", "batchDeleteErrorTip": "批量刪除出錯: {{message}}", "batchDeleteSuccess": "批量刪除成功！", "batchDeleteTip": "是否批量刪除所選渠道？", "deleteTag": "刪除標籤", "deleteTagAndChannels": "刪除標籤及其所有渠道", "deleteTagConfirm": "確定要刪除標籤 {{tag}} 及其所有頻道嗎？此操作將無法恢復。", "deleteTagError": "刪除標籤失敗: {{message}}", "deleteTagSuccess": "標籤 {{tag}} 刪除成功", "disable": "停用", "disableAllChannels": "停用所有渠道", "disableTagChannels": "停用標籤渠道", "enable": "啟用", "enableAllChannels": "啟用所有渠道", "enableTagChannels": "啟用標籤渠道", "getTagChannelsError": "獲取標籤渠道失敗: {{message}}", "getTagChannelsErrorTip": "獲取標籤渠道出錯: {{message}}", "noTagChannels": "未能找到標籤渠道", "priorityUpdateError": "優先級更新失敗: {{message}}", "priorityUpdateSuccess": "優先級更新成功", "refreshList": "更新列表", "tag": "標籤", "tagChannelList": "標籤渠道列表", "tagChannelsConfirm": "確定要{{action}}標籤 {{tag}} 下的所有渠道嗎？", "tagChannelsError": "標籤渠道{{action}}失敗: {{message}}", "tagChannelsSuccess": "標籤渠道{{action}}成功", "weightUpdateError": "權重更新失敗: {{message}}", "weightUpdateSuccess": "權重更新成功", "key": "密鑰", "keyRequired": "請輸入密鑰"}, "common": {"again": "重試 ({{count}})", "back": "返回", "bindOk": "綁定成功！", "cancel": "取消", "close": "關閉", "copyUrl": "複製地址", "create": "創建", "delete": "刪除", "deleteConfirm": "是否刪除 {{title}}?", "disable": "已禁用", "downImg": "下載圖片", "edit": "編輯", "enable": "已啟用", "execute": "是否執行 {{title}}?", "executeConfirm": "執行", "exhaust": "已耗盡", "expired": "已過期", "imgUrl": "圖片地址", "link": "鏈接", "loginOk": "登錄成功！", "newWindos": "新窗口打開", "noData": "暫無數據", "none": "無", "processing": "處理中...", "registerOk": "註冊成功！", "registerTip": "驗證碼發送成功，請檢查你的郵箱！", "saveSuccess": "保存成功！", "serverError": "伺服器錯誤", "show": "顯示", "submit": "提交", "unableServer": "無法正常連接至伺服器！", "unableServerTip": "新版本可用：{{version}}，請使用快捷鍵 Shift + F5 刷新頁面", "unknown": "未知", "verificationCode": "驗證碼", "deleteError": "刪除失敗：{{message}}", "deleteSuccess": "刪除成功！", "jsonFormatError": "JSON 格式錯誤", "actions": "操作", "noDataAvailable": "冇數據可用", "pageSize": "每頁條數", "search": "搜尋", "total": "總數"}, "dashboard": "儀表板", "dashboard_index": {"balance": "餘額", "calls": "調用次數", "model_name": "模型名稱", "model_price": "目前可用模型", "no_data": "無數據", "no_data_available": "暫無數據", "other_models": "其他模型", "statistics": "統計", "today_consumption": "今日消耗", "today_requests": "今日請求", "today_tokens": "今日Token", "unknown": "未知", "used": "已使用", "week_model_statistics": "近七日模型統計", "week_consumption_log": "近七日消費日志", "7days_model_usage_pie": "近七日模型使用情況", "date": "日期", "request_count": "請求次數", "amount": "金額", "tokens": "Tokens(輸入/輸出)", "cache_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request_time": "請求時間(s)", "loading": "加載中...", "quickStart": "快速開始", "quickStartTip": "當您點擊下面按鈕時，系統會自動創建一個sys_playground的令牌", "ComparedWithYesterday": "相比昨日", "usage": "使用率", "RPM": "當前RPM", "TPM": "當前TPM", "total": "總次數", "availability": "可用率", "tab_status": "狀態", "tab_dashboard": "儀表板", "title": "儀表板"}, "description": "一站式的 OpenAI 接口\n整合各種 API 訪問方式\n一鍵部署，開箱即用", "echo 映射": "echo 映射", "error": {"unknownError": "未知錯誤"}, "fable 映射": "fable 映射", "footer": {"basedOn": "基於", "developedBy": "由", "license": "MIT 協議", "sourceCode": "源代碼遵循"}, "home": {"loadingErr": "加載首頁內容失敗..."}, "inviteCard": {"copyButton": {"copy": "複製", "generate": "生成"}, "generateInvite": "點擊生成邀請連結", "inviteDescription": "分享您的邀請連結，邀請好友註冊，即可獲得獎勵！", "inviteReward": "邀請獎勵", "inviteUrlLabel": "邀請連結"}, "jump": "正在跳轉中...", "invoice": "月度賬單", "midjourney": "Midjourney", "invoice_index": {"invoice": "月度賬單", "refresh": "刷新", "id": "ID", "name": "名稱", "status": "狀態", "amount": "金額", "createdTime": "創建時間", "paidTime": "支付時間", "actions": "操作", "deleteInvoice": "刪除賬單", "confirmDeleteInvoice": "是否刪除賬單", "close": "關閉", "delete": "刪除", "paid": "已支付", "unpaid": "未支付", "date": "賬單時間", "requestCount": "請求總數", "quota": "總金額", "promptTokens": "輸入tokens", "completionTokens": "輸出tokens", "requestTime": "請求時長", "tokens": "TOKEN(輸入/輸出)", "alert": "每月1日凌晨生成上月賬單數據", "viewInvoice": "查看賬單", "modelName": "模型名稱", "option": "操作", "username": "用戶名", "email": "郵箱", "userinfo": "用戶信息", "usage_statistics": "使用統計", "download": "下載PDF", "usage_details": "賬單詳情", "summary": "摘要"}, "log": "日誌", "logPage": {"cachedTokens": "緩存Tokens (* {{ ratio }})", "channelLabel": "渠道", "columnSettings": "列設置", "selectColumns": "選擇列", "columnSelectAll": "全選", "detailLabel": "詳情", "durationLabel": "耗時", "durationTooltip": "t/s：輸出Token的數量除以總生成時間，表示生成速度", "groupLabel": "分組", "inputAudioTokens": "輸入音頻Tokens (* {{ ratio }})", "inputLabel": "輸入", "inputTextTokens": "輸入文本Tokens (* {{ ratio }})", "modelLabel": "模型", "outputAudioTokens": "輸出音頻Tokens (* {{ ratio }})", "outputLabel": "輸出", "outputTextTokens": "輸出文本Tokens (* {{ ratio }})", "quotaLabel": "額度", "refreshButton": "刷新/清除搜尋條件", "searchButton": "搜尋", "searchLogsInfo": "充值記錄以及邀請記錄請在日誌中查詢。充值記錄請在日誌中選擇類型【充值】查詢；邀請記錄請在日誌中選擇【系統】查詢", "timeLabel": "時間", "title": "日誌", "tokenLabel": "令牌", "totalInputTokens": "計算輸入Tokens", "totalOutputTokens": "計算輸出Tokens", "typeLabel": "類型", "userLabel": "用戶", "sourceIp": "Source IP", "logType": {"all": "全部", "recharge": "充值", "consumption": "消費", "management": "管理", "system": "系統"}, "content": {"calculate_steps": "計算步驟:", "calculate_steps_tip": "PS：本系統按照積分計算，所有金額均為積分換算而來，1積分=$0.000002，最低消費為1積分，本計算步驟僅供參考，以實際扣費為準。", "channel_group": "分組: {{ channel_group }}", "group_discount": "分組折扣: {{ discount }}", "input_price": "輸入: ${{ price }} /M", "original_input_price": "原輸入價格: ${{ price }} /M", "original_output_price": "原輸出價格: ${{ price }} /M", "original_times_price": "原價格: ${{ times }} / 次", "output_price": "輸出: ${{ price }} /M", "times_price": "${{ times }} / 次", "free": "免費", "old_log": "舊版log", "illustrate": "* 本筆記錄為系統升級前所記錄的log"}, "quotaDetail": {"saved": "節省 ", "originalPrice": "原始價格", "inputPrice": "輸入價格", "outputPrice": "輸出價格", "groupRatio": "分組倍率", "groupRatioValue": "分組倍率", "actualPrice": "實際價格", "input": "輸入", "output": "輸出", "finalCalculation": "最終計算", "originalBilling": "原始計費", "actualBilling": "實際計費", "calculationNote": "PS：本系統按照積分計算，所有金額均為積分換算而來，1積分=$0.000002，最低消費為1積分，本計算步驟僅供參考，以實際扣費為準。", "times": "倍"}, "cachedReadTokens": "緩存讀取Tokens (* {{ ratio }})", "reasoningTokens": "推理Tokens (* {{ ratio }})", "cachedWriteTokens": "緩存寫入 Tokens (* {{ ratio }})"}, "login": {"codeRequired": "驗證碼不能為空", "forgetPassword": "忘記密碼？", "githubCountError": "出現錯誤，第 {{count}} 次重試中...", "githubError": "操作失敗，正在重定向至登錄界面...", "githubLogin": "GitHub 登錄", "larkLogin": "飛書 登錄", "oidcCountError": "出現錯誤，第 {{count}} 次重試中...", "oidcError": "操作失敗，正在重定向至登錄界面...", "oidcLogin": "OIDC 登錄", "password": "密碼", "passwordRequired": "密碼為必填項", "passwordRest": "密碼重置確認", "qrCode": "二維碼", "useGithubLogin": "使用 GitHub 登錄", "useLarkLogin": "使用飛書登錄", "useOIDCLogin": "使用 OIDC 登錄", "useWechatLogin": "使用 Wechat 登錄", "usernameOrEmail": "用戶名/電郵", "usernameRequired": "用戶名/電郵為必填項", "wechatLoginInfo": "請使用微信掃描二維碼關注公眾號，輸入「驗證碼」獲取驗證碼（有效期三分鐘）", "wechatVerificationCodeLogin": "微信驗證碼登錄"}, "menu": {"about": "關於", "console": "控制台", "error": "錯誤", "home": "主頁", "login": "登入", "signout": "登出", "signup": "註冊", "unknownVersion": "未知版本", "welcomeBack": "歡迎回來"}, "midjourneyPage": {"channel": "渠道", "failureReason": "失敗原因", "midjourney": "Midjourney", "progress": "進度", "prompt": "提示", "promptEn": "提示（英文）", "refreshClearSearch": "刷新/清除搜尋條件", "resultImage": "結果圖片", "search": "搜尋", "submissionResult": "提交結果", "submitTime": "提交時間", "taskID": "任務ID", "taskStatus": "任務狀態", "timeConsuming": "耗時", "type": "類型", "user": "用戶"}, "model_price": "可用模型", "modelpricePage": {"availableModels": "可用模型", "channelType": "供應商", "group": "分組", "inputMultiplier": "輸入價格", "model": "模型名稱", "noneGroup": "當前分組不可用", "outputMultiplier": "輸出價格", "search": "搜尋", "times": "按次付費", "tokens": "按量付費", "type": "類型", "input_audio_tokens": "音頻輸入倍率", "other": "其他", "output_audio_tokens": "音頻輸出倍率", "rate": "倍率", "RPM": "API速率", "free": "免費", "cached_tokens": "快取倍率", "cached_write_tokens": "快取寫入倍率", "cached_read_tokens": "快取讀取倍率", "reasoning_tokens": "推理倍率", "input_text_tokens": "輸入文本倍率", "output_text_tokens": "輸出文本倍率", "all": "全部", "extraRatios": "擴展價格", "input": "輸入", "input_image_tokens": "輸入圖片倍率", "noExtraRatios": "冇擴展價格", "output": "輸出", "output_image_tokens": "輸出圖片倍率", "price": "價格", "showAll": "顯示所有", "onlyAvailable": "只顯示可用"}, "nova 映射": "nova 映射", "onyx 映射": "onyx 映射", "operation": "營運", "orderlogPage": {"endTimeLabel": "結束時間", "gatewayIdLabel": "網關ID", "gatewayNoLabel": "網關訂單號", "placeholder": {"gatewayId": "網關ID", "gatewayNo": "網關訂單號", "tradeNo": "訂單號", "userId": "用戶ID"}, "refreshClear": "刷新/清除搜索條件", "search": "搜索", "startTimeLabel": "起始時間", "statusLabel": "狀態", "statusOptions": {"status1": "狀態1", "status2": "狀態2", "status3": "狀態3"}, "tableHeaders": {"amount": "充值金額", "created_at": "時間", "discount": "優惠金額", "fee": "手續費", "gateway_id": "支付網關", "gateway_no": "網關訂單號", "order_amount": "實際支付金額", "quota": "到賬點數", "status": "狀態", "trade_no": "訂單號", "user_id": "用戶"}, "title": "日誌", "tradeNoLabel": "訂單號", "userIdLabel": "用戶ID"}, "paySetting": "支付設定", "payment": "付款", "paymentGatewayPage": {"createPayment": "新建支付", "refreshClear": "刷新/清除搜尋條件", "search": "搜尋", "tableHeaders": {"action": "操作", "createdAt": "創建時間", "enable": "啟用", "fixedFee": "固定手續費", "icon": "圖標", "id": "ID", "name": "名稱", "percentFee": "百分比手續費", "sort": "排序", "type": "類型", "uuid": "UUID"}, "title": "支付網關"}, "paymentPage": {"gatewaySettings": "網關設置", "orderList": "訂單列表"}, "payment_edit": {"FixedTip": "每次支付收取固定的手續費，單位 美元", "addOk": "創建成功！", "currencyTip": "該網關是收取什麼貨幣的，請查詢對應網關文檔", "currencyType": "網關貨幣類型", "notifyDomain": "回調域名", "notifyDomainTip": "支付回調的域名，除非你自行配置過，否則保持為空", "paymentEdit": "編輯支付", "paymentType": "支付類型", "percentTip": "每次支付按百分比收取手續費，如果為5%，請填寫 0.05", "requiredCurrency": "貨幣 不能為空", "requiredFixedFee": "固定手續費 不能小於 0", "requiredIcon": "圖標 不能為空", "requiredPercentFee": "百分比手續費 不能小於 0", "updateOk": "更新成功！"}, "payment_row": {"delPayment": "刪除支付", "delPaymentTip": "是否刪除支付", "sortTip": "排序不能少於 0"}, "pricing": "模型價格", "pricingPage": {"ModelCount": "模型數量", "currencyInfo1": "美元", "currencyInfo2": "：1 === $0.002 / 1K tokens", "currencyInfo3": "港元", "currencyInfo4": "：1 === HK$0.014 / 1k tokens", "currencyInfo5": "例如", "currencyInfo6": "：gpt-4 輸入： $0.03 / 1K tokens 完成：$0.06 / 1K tokens", "currencyInfo7": "0.03 / 0.002 = 15, 0.06 / 0.002 = 30，即輸入倍率為 15，完成倍率為 30", "errPricesWarning": "存在供應商類型錯誤的模型，請及時配置：", "multipleOperation": "合併操作", "newButton": "新建", "noPriceModelWarning": "存在未配置價格的模型，請及時配置價格：", "refreshButton": "刷新", "singleOperation": "單條操作", "updatePricesButton": "更新價格", "title": "模型價格"}, "pricing_edit": {"channelType": "渠道類型", "channelTypeErr": "渠道類型錯誤", "channelTypeErr2": "所屬渠道類型錯誤", "delGroup": "刪除價格組", "delGroupTip": "是否刪除價格組？", "delInfoTip": "確定刪除 {{name}} 嗎？", "delTip": "確定刪除？", "inputVal": "輸入倍率必須大於等於0", "model": "模型", "modelNameRe": "模型名稱不能重複", "modelTip": "請選擇該價格所支持的模型，你也可以輸入通配符*來匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5開頭的模型，*號只能在最後一位使用，前面必須有字符，例如：gpt-3.5*是正確的，*gpt-3.5是錯誤的", "name": "名稱", "outputVal": "輸出倍率必須大於等於0", "requiredChannelType": "渠道類型不能為空", "requiredInput": "輸入倍率不能為空", "requiredModelName": "模型名稱不能為空", "requiredModels": "模型不能為空", "requiredOutput": "輸出倍率不能為空", "requiredType": "類型不能為空", "saveOk": "保存成功", "type": "類型", "typeCheck": "類型只能是tokens或times", "typeErr": "類型錯誤", "locked_title": "鎖定價格", "locked": "鎖定價格", "unlocked": "未鎖定價格", "lockedTip": "價格鎖定後無法通過價格更新伺服器和程序覆蓋。如果您所配置的價格希望隨程序或價格伺服器同步更新，請勿鎖定", "completionRatio": "完成倍率", "delMultipleInfoTip": "確定刪除所選的 {{count}} 個價格嗎？", "noAvailableRatios": "冇可用嘅擴展倍率", "noExtraRatios": "暫無額外擴展倍率，請從下拉框選擇添加", "promptRatio": "提示倍率", "selectExtraRatio": "選擇擴展倍率"}, "profile": "個人設定", "profilePage": {"accountBinding": "帳號綁定", "bindEmail": "綁定電郵", "bindGitHubAccount": "綁定 GitHub 帳號", "bindLarkAccount": "綁定飛書帳號", "bindWechatAccount": "綁定微信帳號", "changeEmail": "更換電郵", "displayName": "顯示名稱", "generateToken": "生成訪問令牌", "inputDisplayNamePlaceholder": "請輸入顯示名稱", "inputPasswordPlaceholder": "請輸入密碼", "inputUsernamePlaceholder": "請輸入用戶名", "keepSafe": "請妥善保管。如有洩漏，請立即重置。", "lark": "飛書", "notBound": "未綁定", "other": "其他", "password": "密碼", "passwordMinLength": "密碼不能少於 8 個字符", "personalInfo": "個人資料", "resetToken": "重置訪問令牌", "submit": "提交", "telegramBot": "Telegram 機器人", "telegramStep1": "1. 點擊下方按鈕，將會在 Telegram 中打開機器人，點擊 /start 開始。", "telegramStep2": "2. 向機器人發送 /bind 命令後，輸入下方的訪問令牌即可綁定。(如果沒有生成，請點擊下方按鈕生成)", "token": "令牌", "tokenNotice": "注意，此處生成的令牌用於系統管理，而非用於請求 OpenAI 相關的服務，請知悉。", "updateSuccess": "用戶資料更新成功！", "username": "用戶名", "usernameMinLength": "用戶名不能少於 3 個字符", "usernameRequired": "用戶名不能為空", "wechatBindSuccess": "微信帳戶綁定成功！", "yourTokenIs": "你的訪問令牌是："}, "redemption": "兌換", "redemptionPage": {"createRedemptionCode": "創建兌換碼", "del": "刪除兌換碼", "delTip": "是否刪除兌換碼", "headLabels": {"action": "操作", "createdTime": "創建時間", "id": "ID", "name": "名稱", "quota": "額度", "redeemedTime": "兌換時間", "status": "狀態"}, "pageTitle": "兌換", "refreshButton": "刷新", "searchPlaceholder": "搜索兌換碼...", "successMessage": "操作成功", "unredeemed": "尚未兌換"}, "redemption_edit": {"addOk": "兌換碼創建成功！", "editOk": "兌換碼更新成功！", "number": "數量", "requiredCount": "必須大於等於1", "requiredQuota": "必須大於等於0"}, "registerForm": {"confirmPasswordRequired": "確認密碼是必填項", "emailRequired": "電郵是必填項", "enterEmail": "請輸入電郵", "getCode": "獲取驗證碼", "passwordRequired": "密碼是必填項", "passwordsNotMatch": "兩次輸入的密碼不一致", "resendCode": "重新發送({{countdown}})", "restSendEmail": "重置郵件發送成功，請檢查電郵！", "turnstileError": "請稍後幾秒重試，Turnstile 正在檢查用戶環境！", "usernameRequired": "用戶名是必填項", "validEmailRequired": "必須是有效的電郵地址", "verificationCodeRequired": "驗證碼是必填項", "verificationInfo": "請稍後幾秒重試，Turnstile 正在檢查用戶環境！"}, "registerPage": {"alreadyHaveAccount": "已經有帳號了? 點擊登入"}, "res_time": {"lastTime": "上次測速時間：", "noTest": "未測試", "second": "秒", "testClick": "點擊測速（僅支持chat模型）"}, "setting": "設定", "theme": {"auto": "跟隨系統", "light": "淺色模式", "dark": "深色模式"}, "setting_index": {"operationSettings": {"chatLinkSettings": {"info": "配置聊天鏈接，該配置在令牌中的聊天生效以及首頁的 Playground 中的聊天生效. <br />鏈接中可以使&#123;key&#125;替換用戶的令牌，&#123;server&#125;替換伺服器地址。例如：{'https://chat.oneapi.pro/#/?settings={\"key\":\"sk-{key}\",\"url\":\"{server}\"}'}<br />如果未配置，會默認配置以下4個鏈接：<br />ChatGPT Next ： {'https://chat.oneapi.pro/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />chatgpt-web-midjourney-proxy ： {'https://vercel.ddaiai.com/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />AMA 問天 ： {'ama://set-api-key?server={server}&key={key}'}<br />opencat ： {'opencat://team/join?domain={server}&token={key}'}<br />排序規則：值越大越靠前，值相同則按照配置順序", "save": "保存聊天鏈接設置", "title": "聊天鏈接設置"}, "generalSettings": {"approximateToken": "使用近似的方式估算 token 數以減少計算量", "chatLink": {"label": "聊天鏈接", "placeholder": "例如 ChatGPT Next Web 的部署地址"}, "displayInCurrency": "以貨幣形式顯示額度", "displayTokenStat": "Billing 相關 API 顯示令牌額度而非用戶額度", "quotaPerUnit": {"label": "單位額度", "placeholder": "一單位貨幣能兌換的額度"}, "retryCooldownSeconds": {"label": "重試間隔(秒)", "placeholder": "重試間隔(秒)"}, "retryTimes": {"label": "重試次數", "placeholder": "重試次數"}, "saveButton": "保存通用設置", "title": "通用設置", "topUpLink": {"label": "充值鏈接", "placeholder": "例如發卡網站的購買鏈接"}, "retryTimeOut": {"label": "重試超時時間(秒)", "placeholder": "重試超時時間(秒)"}, "emptyResponseBilling": "空回覆計費", "unifiedRequestResponseModel": "統一請求響應模型", "unifiedRequestResponseModelTooltip": "當存在模型映射時，響應中的model值為請求的model值，而非實際調用的模型名稱"}, "logSettings": {"clearLogs": "清理歷史日誌", "logCleanupTime": {"label": "日誌清理時間", "placeholder": "日誌清理時間"}, "logConsume": "啟用日誌消費", "title": "日誌設置"}, "monitoringSettings": {"automaticDisableChannel": "失敗時自動禁用通道", "automaticEnableChannel": "成功時自動啟用通道", "channelDisableThreshold": {"label": "最長響應時間", "placeholder": "單位秒，當運行通道全部測試時，超過此時間將自動禁用通道"}, "quotaRemindThreshold": {"label": "額度提醒閾值", "placeholder": "低於此額度時將發送郵件提醒用戶"}, "saveMonitoringSettings": "保存監控設置", "title": "監控設置"}, "otherSettings": {"CFWorkerImageUrl": {"alert": "這裡是 Cloudflare Worker 的圖片代理地址，你可以通過部署 https://github.com/MartialBE/get-image-by-cf，來使用，它和圖片檢測代理可以只設置其中一個。注意有些圖片鏈接可能會拒絕 CF 的訪問導致檢測失敗。", "key": "Cloudflare Worker 圖片代理 key, 如果沒有配置請忽略", "label": "Cloudflare Worker 圖片代理"}, "alert": "當用戶使用 vision 模型並提供了圖片鏈接時，我們的伺服器需要下載這些圖片並計算 tokens。為了在下載圖片時保護伺服器的 IP 地址不被洩露，可以在下方配置一個代理。這個代理配置使用的是 HTTP 或 SOCKS5 代理。如果你是個人用戶，這個配置可以不用理會。代理格式為 http://127.0.0.1:1080 或 socks5://127.0.0.1:1080", "chatImageRequestProxy": {"label": "圖片檢測代理", "placeholder": "聊天圖片檢測代理設置，如果不設置可能會洩漏伺服器 IP"}, "mjNotify": "Midjourney 允許回調（會洩露伺服器 IP 地址）", "saveButton": "保存其他設置", "title": "其他設置", "claudeAPIEnabled": "是否開啟Claude API", "geminiAPIEnabled": "是否開啟Gemini API"}, "paymentSettings": {"alert": "支付設置： <br />1. 美元匯率：用於計算充值金額的美元金額 <br />2. 最低充值金額（美元）：最低充值金額，單位為美元，填寫整數 <br />3. 頁面都以美元為單位計算，實際用戶支付的貨幣，按照支付網關設置的貨幣進行轉換 <br />例如： A 網關設置貨幣為 CNY，用戶支付 100 美元，那麼實際支付金額為 100 * 美元匯率 <br />B 網關設置貨幣為 USD，用戶支付 100 美元，那麼實際支付金額為 100 美元", "discount": {"label": "固定金額充值折扣", "placeholder": "為一個 JSON 文本，鍵為充值金額，值為折扣"}, "discountInfo": "固定金額充值折扣設置示例： <br />為一個 JSON 文本，鍵為充值金額，值為折扣，比如 &#123;&quot;10&quot;:0.9&#125; 表示充值10美元按照9折計算 <br />計算公式：實際費用=（原始價值*折扣+原始價值*折扣*手續費率）*匯率", "minAmount": {"label": "最低充值金額（美元）", "placeholder": "例如：1，那麼最低充值金額為1美元，請填寫整數"}, "save": "保存支付設置", "title": "支付設置", "usdRate": {"label": "美元匯率", "placeholder": "例如：7.3"}}, "quotaSettings": {"preConsumedQuota": {"label": "請求預扣費額度", "placeholder": "請求結束後多退少補"}, "quotaForInvitee": {"label": "新用戶使用邀請碼獎勵額度", "placeholder": "例如：1000"}, "quotaForInviter": {"label": "邀請新用戶獎勵額度", "placeholder": "例如：2000"}, "rechargeRewardType": {"label": "充值返利類型", "fixed": "固定", "percentage": "百分比"}, "rechargeRewardValue": {"label": "充值返利值", "fixedPlaceholder": "例如：2000", "percentagePlaceholder": "例如：10"}, "quotaForNewUser": {"label": "新用戶初始額度", "placeholder": "例如：100"}, "saveQuotaSettings": "保存額度設置", "title": "額度設置"}, "rateSettings": {"groupRatio": {"label": "分組倍率", "placeholder": "為一個 JSON 文本，鍵為分組名稱，值為倍率"}, "save": "保存倍率設置", "title": "倍率設置"}, "title": "運營設置", "extraTokenPriceJson": {"info": "配置額外Token價格。配置格式為JSON，鍵為模型名稱，值為輸入輸出Token價格。例如：{\"gpt-4o-audio-preview\":{\"input_audio_tokens_ratio\":40,\"output_audio_tokens_ratio\":20},\"gpt-4o-mini-audio-preview\":{\"input_audio_tokens_ratio\":67,\"output_audio_tokens_ratio\":34}}", "save": "保存額外Token價格設置", "title": "額外Token價格設定"}, "disableChannelKeywordsSettings": {"info": "配置停用通道關鍵字，每行一個關鍵字。", "save": "保留停用通道關鍵字設置", "title": "停用通道關鍵詞設置"}, "safetySettings": {"title": "系統安全設置", "enableSafe": "開啟 Prompt 安全檢查", "safeToolName": {"label": "安全檢查工具"}, "safeKeyWords": {"label": "關鍵詞列表", "placeholder": "請輸入關鍵詞，每行一個"}, "save": "保存設置"}, "claudeSettings": {"budgetTokensPercentage": {"label": "預設思考Token百分比", "placeholder": "請輸入預設思考 Token 百分比"}, "defaultMaxTokens": {"label": "默認MaxToken數量", "placeholder": "請輸入默認MaxToken數量，以json格式表示，default代表默認值，例如：{\"default\": 1000, \"claude-3-7-sonnet-latest\": 128000}"}, "save": "保留Claude設置", "title": "克勞德設置"}, "geminiSettings": {"title": "Gemini設置", "geminiOpenThink": {"label": "開啟輸出推理的模型", "placeholder": "請輸入需要開啟推理輸出的模型，JSON格式，例如：{\"gemini-2.5-pro-preview-05-06\": true}"}, "save": "保存Gemini設置"}}, "otherSettings": {"customSettings": {"aboutLabel": "關於", "aboutPlaceholder": "在此輸入新的關於內容，支持 Markdown & HTML 代碼。如果輸入的是一個鏈接，則會使用該鏈接作為 iframe 的 src 屬性，這允許你設置任意網頁作為關於頁面。", "copyrightWarning": "移除 Done Hub 的版權標識必須首先獲得授權，項目維護需要花費大量精力，如果本項目對你有意義，請主動支持本項目。", "footerLabel": "頁腳設置", "footerPlaceholder": "在此輸入新的頁腳，留空則使用默認頁腳，支持 HTML 代碼", "homePageContentLabel": "首頁內容", "homePageContentPlaceholder": "在此輸入首頁內容，支持 Markdown & HTML 代碼，設置後首頁的狀態信息將不再顯示。如果輸入的是一個鏈接，則會使用該鏈接作為 iframe 的 src 屬性，這允許你設置任意網頁作為首頁。", "logoLabel": "Logo 圖片地址", "logoPlaceholder": "在此輸入 Logo 圖片地址", "saveAbout": "保存關於", "saveHomePageContent": "保存首頁內容", "setFooter": "設置頁腳", "setLogo": "設置 Logo", "setSystemName": "設置系統名稱", "systemNameLabel": "系統名稱", "systemNamePlaceholder": "在此輸入系統名稱", "title": "個性化設置"}, "generalSettings": {"checkUpdate": "檢查更新", "currentVersion": "當前版本", "noticeLabel": "公告", "noticePlaceholder": "在此輸入新的公告內容，支持 Markdown & HTML 代碼", "saveNotice": "保存公告", "title": "通用設置"}, "title": "其他設置", "updateDialog": {"close": "關閉", "newVersion": "新版本", "viewGitHub": "去 GitHub 查看"}}, "systemSettings": {"configureEmailDomainWhitelist": {"allowedEmailDomains": "允許的郵箱域名", "emailDomainRestriction": "啟用郵箱域名白名單", "save": "保存郵箱域名白名單設置", "subTitle": "用以防止惡意用戶利用臨時郵箱批量註冊", "title": "配置郵箱域名白名單"}, "configureFeishuAuthorization": {"alert1": "首頁鏈接填", "alert2": "，重定向 URL 填 ", "appId": "App ID", "appIdPlaceholder": "輸入 App ID", "appSecret": "App Secret", "appSecretPlaceholder": "敏感信息不會發送到前端顯示", "manage": "管理你的飛書應用", "manageLink": "點擊此處", "saveButton": "保存飛書 OAuth 設置", "subTitle": "用以支持通過飛書進行登錄註冊，", "title": "配置飛書授權"}, "configureGitHubOAuthApp": {"alert1": "Homepage URL 填", "alert2": "，Authorization callback URL 填", "clientId": "GitHub Client ID", "clientIdPlaceholder": "輸入你註冊的 GitHub OAuth APP 的 ID", "clientSecret": "GitHub Client Secret", "clientSecretPlaceholder": "敏感信息不會發送到前端顯示", "manage": "管理你的 GitHub OAuth App", "manageLink": "點擊此處", "saveButton": "保存 GitHub OAuth 設置", "subTitle": "用以支持通過 GitHub 進行登錄註冊，", "title": "配置 GitHub OAuth 應用"}, "configureLoginRegister": {"emailVerification": "通過密碼註冊時需要進行郵箱驗證", "gitHubOAuth": "允許通過 GitHub 賬戶登錄 & 註冊", "larkAuth": "允許通過飛書登錄 & 註冊", "oidcAuth": "允許通過 OIDC 賬戶登錄 & 註冊", "passwordLogin": "允許通過密碼進行登錄", "passwordRegister": "允許通過密碼進行註冊", "registerEnabled": "允許新用戶註冊（此項為否時，新用戶將無法以任何方式進行註冊）", "title": "配置登錄和註冊", "turnstileCheck": "啟用 Turnstile 用戶校驗", "weChatAuth": "允許通過微信登錄 & 註冊", "gitHubOldIdClose": "關閉 GitHub 老 ID 登錄"}, "configureOIDCAuthorization": {"alert1": "首頁鏈接填", "alert2": "，重定向 URL 填 ", "clientId": "客戶端 ID（Client ID）", "clientIdPlaceholder": "請輸入客戶端 ID", "clientSecret": "客戶端密鑰(Secret)", "clientSecretPlaceholder": "敏感信息不會發送到前端顯示", "issuer": "OIDC 發行者(Issuer)", "issuerPlaceholder": "請輸入 OIDC 發行者", "saveButton": "保存 OIDC 設置", "scopes": "權限範圍（Sc<PERSON><PERSON>）", "scopesPlaceholder": "請輸入權限範圍（用英文逗號分隔）, 通常為 'openid,email,profile'", "subTitle": "用以配置標準 OIDC 授權登錄系統", "title": "配置 OIDC 統一授權系統", "usernameClaims": "用戶名聲明（Claims）", "usernameClaimsPlaceholder": "請輸入用戶名聲明(例如 username)"}, "configureSMTP": {"alert": "請注意，有些郵箱服務商發送郵件時會攜帶你的伺服器 IP 地址，非個人使用時建議使用專業的郵件服務商", "save": "保存 SMTP 設置", "smtpAccount": "SMTP 賬戶", "smtpAccountPlaceholder": "通常是郵箱地址", "smtpFrom": "SMTP 發送者郵箱", "smtpFromPlaceholder": "通常和郵箱地址保持一致", "smtpPort": "SMTP 端口", "smtpPortPlaceholder": "默認: 587", "smtpServer": "SMTP 伺服器地址", "smtpServerPlaceholder": "例如：smtp.qq.com", "smtpToken": "SMTP 訪問憑證", "smtpTokenPlaceholder": "敏感信息不會發送到前端顯示", "subTitle": "用以支持系統的郵件發送", "title": "配置 SMTP"}, "configureTurnstile": {"manage": "管理你的 Turnstile Sites，推薦選擇 Invisible Widget Type", "manageLink": "點擊此處", "saveButton": "保存 Turnstile 設置", "secretKey": "Turnstile Secret Key", "secretKeyPlaceholder": "敏感信息不會發送到前端顯示", "siteKey": "Turnstile Site Key", "siteKeyPlaceholder": "輸入你註冊的 Turnstile Site Key", "subTitle": "用以支持用戶校驗，", "title": "配置 Turnstile"}, "configureWeChatServer": {"accessToken": "WeChat Server 訪問憑證", "accessTokenPlaceholder": "敏感信息不會發送到前端顯示", "learn": "了解 WeChat Server", "learnLink": "點擊此處", "qrCodeImage": "微信公眾號二維碼圖片鏈接", "qrCodeImagePlaceholder": "輸入一個圖片鏈接", "saveButton": "保存 WeChat Server 設置", "serverAddress": "WeChat Server 伺服器地址", "serverAddressPlaceholder": "例如：https://yourdomain.com", "subTitle": "用以支持通過微信進行登錄註冊，", "title": "配置 WeChat 伺服器"}, "generalSettings": {"serverAddress": "伺服器地址", "serverAddressPlaceholder": "例如：https://yourdomain.com", "title": "系統設置", "updateServerAddress": "更新伺服器地址"}, "title": "系統設置"}}, "shimmer 映射": "shimmer 映射", "suno": {"lyrics": "歌詞", "music": "音樂", "response": "響應體", "video": "影片"}, "tableToolBar": {"channelId": "渠道ID", "channelIdPlaceholder": "渠道ID", "endTime": "結束時間", "modelName": "模型名稱", "startTime": "開始時間", "taskId": "任務ID", "taskIdPlaceholder": "任務ID", "tokenName": "令牌名稱", "type": "類型", "username": "用戶名稱", "sourceIp": "Source IP"}, "task": "非同步任務", "taskPage": {"channel": "渠道", "fail": "失敗原因", "finishTime": "完成時間", "platform": "平台", "progress": "進度", "status": "任務狀態（點擊查看結果）", "subTime": "提交時間", "task": "任務ID", "time": "耗時", "type": "類型", "user": "用戶", "title": "非同步任務"}, "telegramPage": {"action": "操作", "command": "命令", "createMenu": "新建", "description": "說明", "id": "ID", "infoMessage": "添加修改菜單命令/說明後（如果沒有修改命令和說明可以不用重載），需要重新載入菜單才能生效。如果未查看到新菜單，請嘗試殺掉後台後重新啟動程序。", "offline": "離線", "online": "在線", "operationSuccess": "操作成功完成！", "refresh": "刷新", "reloadMenu": "重新載入菜單", "reloadSuccess": "重載成功！", "replyContent": "回覆內容", "replyType": "回覆類型", "searchPlaceholder": "搜索ID和命令...", "title": "Telegram Bot菜單"}, "telegram_edit": {"addOk": "菜單創建成功！", "msgInfo": "消息內容", "msgType": "消息類型", "requiredCommand": "命令 不能為空", "requiredDes": "說明 不能為空", "requiredMes": "消息內容 不能為空", "requiredParseMode": "消息類型 不能為空", "updateOk": "菜單更新成功！"}, "token": "令牌", "token_index": {"actions": "操作", "cancel": "取消", "chat": "聊天", "close": "關閉", "confirmDeleteToken": "是否刪除令牌", "copy": "複製", "createToken": "新建令牌", "createdTime": "創建時間", "delete": "刪除", "deleteToken": "刪除令牌", "editToken": "編輯令牌", "enableCache": "是否開啟緩存（開啟後，將會緩存聊天記錄，以減少消費）", "expiryTime": "過期時間", "invalidDate": "無效的日期", "name": "名稱", "neverExpires": "永不過期", "quota": "額度", "quotaNote": "注意，令牌的額度僅用於限制令牌本身的最大額度使用量，實際的使用受帳戶的剩餘額度限制。", "refresh": "刷新", "remainingQuota": "剩餘額度", "replaceApiAddress1": "將OpenAI API基礎地址https://api.openai.com替換為", "replaceApiAddress2": "，複製下面的密鑰即可使用。", "searchTokenName": "搜索令牌的名稱...", "status": "狀態", "submit": "提交", "token": "令牌", "unlimited": "無限制", "unlimitedQuota": "無限額度", "usedQuota": "已用額度", "userGroup": "分組", "apiRate": "API速率", "apiRateTip": "每分鐘允許的請求數,當速率小於60時，使用計數器限制器，當速率大於等於60時，使用令牌桶限制器，僅在啟用Redis時有效", "heartbeat": "心跳設置(實驗性)", "heartbeatTip": "心跳設置是指當在請求時，如果長時間沒有返回數據，您的客戶端可能會因為超時機制而斷開連接。為了防止這種情況，您可以開啟心跳設置，當請求超出您設置的開始時間，且無響應時，我們將會每隔5秒發送一次心跳請求(非流式請求返回空行，流式返回::PING)，以保持連接。注意：如果您在使用中轉程序時，請不要開啟該設置，可能會出現不可預知的问题。", "heartbeatTimeout": "心跳開始時間(單位：秒)", "heartbeatTimeoutHelperText": "最小值為30秒，最大值為90秒"}, "topup": "儲值", "topupCard": {"actualAmountToPay": "實際支付金額", "adminSetupRequired": "管理員尚未設置充值鏈接！", "amount": "充值金額", "amountMaxLimit": "金額不能超過 1000000", "amountMinLimit": "金額不能少於", "currentQuota": "當前額度", "discountedPrice": "折扣後價格", "exchangeButton": {"default": "兌換", "submitting": "提交中"}, "exchangeRate": "匯率", "fee": "手續費", "getRedemptionCode": "獲取兌換碼", "inputLabel": "兌換碼", "inputPlaceholder": "請輸入兌換碼！", "noRedemptionCodeText": "還沒有兌換碼？ 點擊獲取兌換碼：", "onlineTopup": "在線充值", "positiveIntegerAmount": "請輸入正整數金額", "redemptionCodeTopup": "兌換碼充值", "selectPaymentMethod": "請選擇支付方式", "topup": "充值", "topupAmount": "充值金額", "topupsuccess": "充值成功！"}, "topupPage": {"alertMessage": "充值記錄以及邀請記錄請在日誌中查詢。充值記錄請在日誌中選擇類型【充值】查詢；邀請記錄請在日誌中選擇【系統】查詢"}, "ui-component": {"allModels": "查看所有模型", "modelName": "模型名稱"}, "user": "用戶", "userGroup": {"create": "新建分組", "enable": "是否啟用", "id": "ID", "name": "名稱", "nameTip": "給用戶看的名稱", "public": "是否公開", "promotion": "自動升級", "promotionTip": "啟用後，用戶充值金額滿足最小-最大條件時將自動升級到此用戶組", "min": "最小金額", "minTip": "自動升級所需的最小充值金額", "max": "最大金額", "maxTip": "此用戶組級別的最大充值金額", "ratio": "倍率", "symbol": "標識", "symbolTip": "標識用於區分用戶組，請使用英文，不可重複", "title": "用戶分組", "apiRate": "API速率", "apiRateTip": "每分鐘允許的請求數，當速率小於60時，使用計數器限制器，當速率大於等於60時，使用令牌桶限制器，僅在啟用Redis時有效。"}, "userPage": {"action": "操作", "adminUserRole": "管理員", "bind": "綁定", "cUserRole": "普通用戶", "cancel": "取消", "cancelAdmin": "取消管理員", "createUser": "新建用戶", "creationTime": "創建時間", "del": "刪除用戶", "delTip": "是否刪除用戶", "displayName": "顯示名稱", "editUser": "編輯用戶", "group": "分組", "groupRequired": "用戶組不能為空", "id": "ID", "operationSuccess": "操作成功完成！", "password": "密碼", "passwordRequired": "密碼不能為空", "quota": "配額", "quotaMin": "配額不能小於 0", "refresh": "刷新", "saveSuccess": "保存成功！", "searchPlaceholder": "搜索用戶的ID、用戶名、分組、顯示名稱，以及電子郵件地址...", "setAdmin": "設為管理員", "statistics": "統計信息", "status": "狀態", "submit": "提交", "superAdminRole": "超級管理員", "uUserRole": "未知身份", "useQuota": "請求次數", "userRole": "用戶角色", "username": "用戶名", "usernameRequired": "用戶名不能為空", "users": "用戶", "changeQuota": "增減額度", "changeQuotaHelperText": "呢度係增減，唔係直接更改用戶餘額，輸入美元，你最多可以扣除 {{quota}}", "changeQuotaNotEmpty": "請填寫更改額度", "changeQuotaNotEnough": "唔可以扣除超過用戶餘額嘅金額", "quotaRemark": "備註"}, "user_group": "用戶分組", "validation": {"requiredName": "名稱 不能為空"}, "仅支持聊天": "僅支持聊天", "从Cohere获取模型列表": "從Cohere獲取模型列表", "从Deepseek获取模型列表": "從Deepseek獲取模型列表", "从Gemini获取模型列表": "從Gemini獲取模型列表", "从Groq获取模型列表": "從Groq獲取模型列表", "从Mistral获取模型列表": "從Mistral獲取模型列表", "从Moonshot获取模型列表": "從Moonshot獲取模型列表", "从OpenAI获取模型列表": "從OpenAI獲取模型列表", "从渠道获取模型列表": "從渠道獲取模型列表", "代理地址": "代理地址", "代码执行": "代碼執行", "位置/区域": "位置/地區", "你可以为你的渠道打一个标签，打完标签后，可以通过标签进行批量管理渠道，注意：设置标签后某些设置只能通过渠道标签修改，无法在渠道列表中修改。": "你可以為你的渠道打上一個標籤，打完標籤後，可以通過標籤進行批量管理渠道，注意：設置標籤後某些設置只能通過渠道標籤修改，無法在渠道列表中修改。", "使用代码执行功能，开启后，计算tokens不准确，建议个人使用开启": "使用代碼執行功能，開啟後，計算tokens不準確，建議個人使用開啟", "使用网页搜索功能，对用户输入的内容进行搜索": "使用網頁搜索功能，對用戶輸入的內容進行搜索", "其他参数": "其他參數", "可空，请输入中转API地址，例如通过cloudflare中转": "可空，請輸入中轉API地址，例如通過cloudflare中轉。支持使用{model}變數，例如：https://api.example.com/v1/{model}/chat", "启用": "啟用", "地址填写Suno-API部署的地址": "地址填寫Suno-API部署的地址", "地址填写midjourney-proxy部署的地址": "地址填寫midjourney-proxy部署的地址", "声音映射": "聲音映射", "如果选择了仅支持聊天，那么遇到有函数调用的请求会跳过该渠道": "如果選擇了僅支持聊天，那麼遇到有函數調用的請求會跳過該渠道", "密钥": "密鑰", "密钥填写Suno-API的密钥，如果没有设置密钥，可以随便填": "密鑰填寫Suno-API的密鑰，如果沒有設置密鑰，可以隨便填", "密钥填写midjourney-proxy的密钥，如果没有设置密钥，可以随便填": "密鑰填寫midjourney-proxy的密鑰，如果沒有設置密鑰，可以隨便填", "将OpenAI的声音角色映射到azure的声音角色, 如果有role，请用|隔开，例如zh-CN-YunxiNeural|boy": "將OpenAI的聲音角色映射到azure的聲音角色，如果有role，請用|隔開，例如zh-HK-YunxiNeural|boy", "当涉及到知识库ID时，请前往开放平台的知识库模块进行创建或获取(是知识库ID不是文档ID！)": "當涉及到知識庫ID時，請前往開放平台的知識庫模塊進行創建或獲取（是知識庫ID不是文檔ID！）", "必须填写所有数据后才能获取模型列表": "必須填寫所有資料後才能獲取模型列表", "按照如下格式输入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041": "按照如下格式輸入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041", "按照如下格式输入：APIKey|SecretKey": "按照如下格式輸入：APIKey|SecretKey", "按照如下格式输入：APISecret|groupID": "按照如下格式輸入：APISecret|groupID", "按照如下格式输入：APPID|APISecret|APIKey": "按照如下格式輸入：APPID|APISecret|APIKey", "按照如下格式输入：AppId|SecretId|SecretKey": "按照如下格式輸入：AppId|SecretId|SecretKey", "按照如下格式输入：CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN": "按照如下格式輸入：CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN", "按照如下格式输入：Region|AccessKeyID|SecretAccessKey|SessionToken 其中SessionToken可不填空": "按照如下格式輸入：Region|AccessKeyID|SecretAccessKey|SessionToken 其中SessionToken可不填空", "按照如下格式输入：SecretId|SecretKey": "按照如下格式輸入：SecretId|SecretKey", "插件参数": "插件參數", "是否启用代码执行": "是否啟用代碼執行", "是否启用网页搜索": "是否啟用網頁搜索", "替换 API 版本": "替換 API 版本", "本配置主要是用于使用cloudflare Zero Trust将端口暴露到公网时，需要配置的header": "本配置主要是用於使用cloudflare Zero Trust將端口暴露到公網時，需要配置的header", "标签": "標籤", "模型": "模型", "模型名称为coze-{bot_id}，你也可以直接使用 coze-* 通配符来匹配所有coze开头的模型": "模型名稱為coze-{bot_id}，你也可以直接使用 coze-* 通配符來匹配所有coze開頭的模型", "模型名称映射， 你可以取一个容易记忆的名字来代替coze-{bot_id}，例如：{\"coze-translate\": \"coze-xxxxx\"},注意：如果使用了模型映射，那么上面的模型名称必须使用映射前的名称，上述例子中，你应该在模型中填入coze-translate(如果已经使用了coze-*，可以忽略)。": "模型名稱映射，你可以取一個容易記憶的名字來代替coze-{bot_id}，例如：{\"coze-translate\": \"coze-xxxxx\"}，注意：如果使用了模型映射，那麼上面的模型名稱必須使用映射前的名稱，上述例子中，你應該在模型中填入coze-translate（如果已經使用了coze-*，可以忽略）。", "模型映射关系": "模型映射關係", "测速模型": "測速模型", "渠道API地址": "渠道API地址", "渠道名称": "渠道名稱", "渠道类型": "渠道類型", "版本号": "版本號", "用于测试使用的模型，为空时无法测速,如：gpt-3.5-turbo，仅支持chat模型": "用於測試的模型，若為空則無法進行測試，例如：gpt-3.5-turbo，僅支持聊天模型", "用户组": "用戶組", "知识库": "知識庫", "知识库ID": "知識庫ID", "知识库模板": "知識庫模板", "网页搜索": "網頁搜尋", "请为渠道命名": "請為渠道命名", "请前往开放平台的知识库上传文档，然后使用知识库功能进行检索。": "請前往開放平台的知識庫上傳文檔，然後使用知識庫功能進行檢索。", "请参考wiki中的文档获取key. https://github.com/MartialBE/one-hub/wiki/VertexAI": "請參考wiki中的文檔獲取key. https://github.com/MartialBE/one-hub/wiki/VertexAI", "请填写AZURE_OPENAI_ENDPOINT": "請填寫AZURE_OPENAI_ENDPOINT", "请求模型时的知识库模板, 请查看文档填写，否则不用填写": "請求模型時的知識庫模板，請查看文件填寫，否則不用填寫", "请输入你 Speech Studio 的位置/区域，例如：eastasia": "請輸入你 Speech Studio 的位置/區域，例如：eastasia", "请输入你部署的Ollama地址，例如：http://127.0.0.1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": "請輸入你部署的Ollama地址，例如：http://127.0.0.1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授權信息", "请输入插件参数，即 X-DashScope-Plugin 请求头的取值": "請輸入插件參數，即 X-DashScope-Plugin 請求頭的取值", "请输入渠道对应的鉴权密钥": "請輸入渠道對應的鑑權密鑰", "请输入版本号，例如：v1": "請輸入版本號，例如：v1", "请输入版本号，例如：v3.1": "請輸入版本號，例如：v3.1", "请输入默认API版本，例如：2024-05-01-preview": "請輸入默認API版本，例如：2024-05-01-preview", "请选择渠道类型": "請選擇渠道類型", "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的": "請選擇該渠道所支持的模型，你也可以輸入通配符*來匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5開頭的模型，*號只能在最後一位使用，前面必須有字符，例如：gpt-3.5*是正確的，*gpt-3.5是錯誤的", "请选择该渠道所支持的用户组": "請選擇該渠道所支持的用戶組", "请随意填写": "請隨意填寫", "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx.com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "輸入後，會替換請求地址中的v1，例如：freeapi，則請求chat時會變成https://xxx.com/freeapi/chat/completions，如果需要禁用版本號，請輸入 disable", "这里选择预计费选项，用于预估费用，如果你觉得计算图片占用太多资源，可以选择关闭图片计费。但是请注意：有些渠道在stream下是不会返回tokens的，这会导致输入tokens计算错误。": "這裡選擇預計費用選項，用於預估費用，如果你覺得計算圖片佔用太多資源，可以選擇關閉圖片計費。但是請注意：有些渠道在stream下是不會返回tokens的，這會導致輸入tokens計算錯誤。", "预计费选项": "預計費用選項", "默认 API 版本": "默認 API 版本", "默认 zh-CN-XiaochenNeural": "默認 zh-HK-XiaochenNeural", "默认 zh-CN-XiaohanNeural": "默認 zh-HK-XiaohanNeural", "默认 zh-CN-YunxiNeural": "默認 zh-HK-YunxiNeural", "默认 zh-CN-YunxiNeural|boy": "默認 zh-HK-YunxiNeural|boy", "默认 zh-CN-YunyangNeural": "默認 zh-HK-YunyangNeural", "默认 zh-CN-YunyeNeural": "默認 zh-HK-YunyeNeural", "playground": "聊天", "modelOwnedby": {"action": "操作", "create": "新建模型歸屬", "icon": "圖標", "iconTip": "圖標用於在模型清單中顯示", "id": "身分證", "idTip": "渠道ID，數字，請設定大於1000以上的數字，設定好不可更改。", "name": "名稱", "nameTip": "渠道名稱", "title": "模型歸屬"}, "price": "價格", "模型映射关系：例如用户请求A模型，实际转发给渠道的模型为B。在B模型加前缀+，表示使用传入模型计费，例如：+gpt-3": {"5-turbo": "模型映射關係：例如用戶請求A模型，實際轉發給渠道的模型為B。在B模型加前綴+，表示使用傳入模型計費，例如：+gpt-3.5-turbo"}, "单独设置代理地址，支持http和socks5，例如：http://127[0][0]": {"1:1080,代理地址中可以通过 `%s` 作为会话标识占位符，程序中检测到有占位符会根据Key生成唯一会话标识符进行替换": "單獨設置代理地址，支持http和socks5，例如：http://127.0.0.1:1080,代理地址中可以通過 `%s` 作為會話標識佔位符，程式中檢測到有占位符會根據Key生成唯一會話標識符進行替換"}, "用于测试使用的模型，为空时无法测速,如：gpt-3": {"5-turbo，仅支持chat模型": "用於測試使用的模型，為空時無法測速，例如：gpt-3.5-turbo，僅支持chat模型", "__i18n_ally_root__": {"__i18n_ally_root__": {"5-turbo，仅支持chat模型": ""}}}, "请参考wiki中的文档获取key": {" https://github": {"com/MartialBE/one-hub/wiki/VertexAI": "請參考wiki中的文件獲取key。https://github.com/MartialBE/one-hub/wiki/VertexAI"}, "__i18n_ally_root__": {"__i18n_ally_root__": {" https://github": {"com/MartialBE/one-hub/wiki/VertexAI": ""}}}}, "请输入你部署的Ollama地址，例如：http://127": {"0": {"0": {"1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": "請輸入您部署的Ollama地址，例如：http://127.0.0.1:11434，如果您使用了Cloudflare Zero Trust，可以在下方插件填入授權資訊"}}}, "请输入版本号，例如：v3": {"1": "請輸入版本號，例如：v3.1", "__i18n_ally_root__": {"__i18n_ally_root__": {"1": ""}}}, "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": "請選擇該渠道所支持的模型，你亦可以輸入通配符*來匹配模型，例如：gpt-3.5*，表示支持所有 gpt-3.5 開頭的模型，*號只能在最後一位使用，前面必須有字符，例如：gpt-3.5*是正確的，*gpt-3.5是錯誤的。"}}}, "__i18n_ally_root__": {"__i18n_ally_root__": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": ""}}}, "__i18n_ally_root__": {"__i18n_ally_root__": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": ""}}}}}}}}, "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx": {"com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "輸入後，會替換請求地址中的v1，例如：freeapi，則請求chat時會變成https://xxx.com/freeapi/chat/completions。如果需要禁用版本號，請輸入 disable。", "__i18n_ally_root__": {"__i18n_ally_root__": {"com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "", "__i18n_ally_root__": {"__i18n_ally_root__": {"com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": ""}}}}}, "禁用流式的模型": "停用流動式的模型", "这里填写禁用流式的模型，注意：如果填写了禁用流式的模型，那么这些模型在流式请求时会跳过该渠道": "呢度填寫禁用流式嘅模型，注意：如果填寫咗禁用流式嘅模型，咁就會喺流式請求時跳過呢個渠道。"}