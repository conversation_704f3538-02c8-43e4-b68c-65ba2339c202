{"System Logs": "System Logs", "view_more_logs_on_server": "To view more system logs, please go to the server", "systemInfo": "System Info", "Auto Refresh": "Auto Refresh", "Refresh Interval": "Refresh Interval", "1 second": "1 second", "3 seconds": "3 seconds", "5 seconds": "5 seconds", "10 seconds": "10 seconds", "30 seconds": "30 seconds", "1 minute": "1 minute", "Max Entries": "Max Entries", "Clear": "Clear", "No logs available": "No logs available", "No matching logs found": "No matching logs found", "Search logs...": "Search logs...", "invite_count": "Invite <PERSON>", "invite_reward": "<PERSON><PERSON><PERSON>", "AZURE_OPENAI_ENDPOINT": "AZURE_OPENAI_ENDPOINT", "CheckUpdatesTable": {"addNewOnly": "Add New Only", "cancel": "Cancel", "checkUpdates": "Check Updates", "dataFormatIncorrect": "Data format is incorrect", "fetchData": "Fetch Data", "inputMultiplierChanged": "Input multiplier changed from", "newModels": "New Models", "noNewModels": "No new models", "noUpdates": "No Updates", "note": "Note", "operationCompleted": "Operation completed successfully!", "outputMultiplierChanged": "Output multiplier changed from", "overwriteData": "Overwrite Data", "overwriteOrAddOnly": "You can choose to overwrite or add only new models. If you choose to overwrite, it will delete the prices of the models you added yourself and use the remote configuration completely. If you choose to add only new models, it will only add the prices of the new models.", "pleaseFetchData": "Please fetch data first", "priceChangeModels": "Price Change Models (for reference only, ignore if you have modified the prices yourself)", "to": "to", "url": "URL", "priceServerTotal": "Price Server Total", "updateModeAdd": "Add Only", "updateModeUpdate": "Update Only", "updateModeOverwrite": "Overwrite All", "added": "Add", "extraRatiosAdded": "Increase the expansion rate", "extraRatiosChanged": "Changes in magnification ratio", "extraRatiosChanges": "Changes in magnification ratio", "extraRatiosChangesTip": "Changes in magnification: {{changes}}", "extraRatiosRemoved": "Remove magnification ratio", "modified": "Edit", "removed": "Delete"}, "menu": {"home": "Home", "about": "About", "status": "Status", "login": "<PERSON><PERSON>", "signup": "Sign up", "signout": "Sign out", "console": "<PERSON><PERSON><PERSON>", "error": "Error", "unknownVersion": "Unknown Version", "welcomeBack": "Welcome Back"}, "sidebar": {"totalQuota": "Total Quota", "remainingBalance": "Remaining Balance"}, "Header 配置": "Header configuration", "about": {"aboutDescription": "You can set the about content in the settings page, supports HTML & Markdown", "aboutTitle": "About", "loadingError": "Failed to load about content...", "projectRepo": "Project repository:"}, "alloy 映射": "alloy mapping", "analytics": "Analytics", "analytics_index": {"active": "Active", "averageLatency": "Average Latency", "channelCount": "Channel Count", "consumptionStatistics": "Consumption Statistics", "directRegistration": "Direct Registration", "disabled": "Disabled", "endTime": "End Time", "invitationRegistration": "Invitation Registration", "redeemCodeIssued": "Redeem Code Issued", "redemptionStatistics": "Redemption Statistics", "registrationStatistics": "Registration Statistics", "requestsCount": "Requests Count", "startTime": "Start Time", "testDisabled": "Test Disabled", "tokensStatistics": "Tokens Statistics", "totalUserBalance": "Total User Balance", "totalUserSpending": "Total User Spending", "totalUsers": "Total Users", "unused": "Unused", "used": "Used", "rechargeStatistics": "Recharge Statistics", "redemptionCode": "Redemption Code", "order": "Order", "realTimeTraffic": "Real-time Traffic", "tpmDescription": "TPM (Token/min)", "last60SecondsStats": "Last 60 seconds statistics", "timeFilter": {"all": "All", "year": "This Year", "month": "This Month", "week": "This Week", "day": "Today"}}, "auth": {"invalidLink": "Invalid link", "newPassword": "New Password", "newPasswordEdit": "Please change your password promptly after logging in", "newPasswordInfo": "Your new password is:", "restPassword": "reset Password", "restPasswordClick": "Click to reset password"}, "channel": "Channel", "channel_edit": {"addModelHeader": "Add Custom Header", "addModelMapping": "Add model mapping", "addModelMappingByJson": "Add model mapping (JSON)", "addSuccess": "Created successfully!", "batchAdd": "Add in batches", "batchBaseurlTip": ", one per line, the order corresponds to the following keys. If the corresponding key cannot be matched, the first one will be used by default.", "batchKeytip": ", one key per line", "collapse": "Collapse", "customModelTip": "Customize: Click or press Enter to enter", "editSuccess": "update completed!", "expand": "Expand", "inputAllModel": "Fill in all models", "inputChannelModel": "Fill in the channel support model", "invalidJson": "Invalid JSON", "isEnable": "Whether to enable", "jsonInputLabel": "JSON object, where the key is the request model and the value is the actual forwarding model.", "modelHeaderKey": "Custom Header Key", "modelHeaderValue": "Custom Header Value", "modelListError": "Failed to get model list", "modelMappingKey": "User request model", "modelMappingValue": "Actual forwarding model", "requiredBaseUrl": "Channel API address cannot be empty", "requiredChannel": "Channel cannot be empty", "requiredGroup": "User group cannot be empty", "requiredKey": "Key cannot be empty", "requiredModels": "Model cannot be empty", "requiredName": "Name is required", "validJson": "Must be a valid JSON string", "copyModels": "Copy model", "mapAdd": "Add {{name}}", "mapAddByJson": "Add {{name}} through JSON", "mapJsonInput": "JSON input", "listJsonError": "JSON format error, please make sure the input is in array format.", "listJsonHelperText": "Please enter the JSON array format, for example: [\"item1\", \"item2\", \"item3\"]", "modelsFetched": "Models fetched successfully", "selectedMappingCount": "Selected {{count}} model mappings", "addMapping": "Add model mapping", "addToModelMapping": "Add to model mapping", "modelMapping": "Model mapping", "modelMappingSettings": "Model mapping settings", "prefixOrSuffix": "Prefix/Suffix", "removePrefixHelp": "Enter the prefix you want to remove", "removeSuffixHelp": "Enter the suffix you want to remove", "addPlusSign": "Use mapping before billing", "mappingPreview": "Mapping preview", "removePrefix": "Remove prefix", "removeSuffix": "Remove suffix", "collapseList": "Collapse list", "expandList": "Expand list", "convertToLowercase": "Convert model names to lowercase", "filterMappedModels": "Do not fill the model name before mapping.", "overwriteModels": "Overwrite existing model list", "overwriteModelsTip": "When enabled, the existing model list will be cleared and only the selected models will be preserved", "overwriteMappings": "Overwrite existing mappings", "overwriteMappingsTip": "When enabled, existing mappings will be cleared and only new mappings will be preserved", "clearModels": "Reset", "clearModelsTip": "Reset all selected models"}, "channel_index": {"AzureApiVersion": "Azure version number", "actions": "Actions", "all": "All", "batchAzureAPISuccess": "Successfully updated {{count}} pieces of data", "batchDelete": "Delete models in batches", "batchDeleteModel": "Please enter the full model name", "batchDeleteSuccess": "Successfully deleted {{count}} pieces of data", "batchDeleteTip": "If the channel has only one model, it will not be displayed. Please manually delete the channel from the list.", "batchProcessing": "Batch Processing", "channel": "Channel", "channelList": "Channel List", "channelName": "Channel Name", "channelTags": "Channel Tags", "channelType": "Channel Type", "deleteDisabledChannels": "Delete Disabled Channels", "description1": "1. The larger the priority, the higher the priority of use; (only when all nodes under this priority are frozen or disabled, will the lower priority nodes be used)", "description2": "2. Under the same priority: Load balancing based on weight (weighted random selection)", "description3": "3. If 'Retry Times' and 'Retry Interval' are set in Settings - General Settings, retries will occur after a failure.", "description4": "4. Retry logic: 1) Retry in nodes with higher priority first. If all nodes in higher priority are frozen, retries will be attempted in nodes with lower priority. 2) If 'Retry Interval' is set, a channel will be frozen for a period after a failure, and no one will use this channel until the freezing time ends. 3) After the retry times are used up, it will end directly.", "disabled": "Disabled", "enabled": "Enabled", "filterTags": "Filter Tags", "group": "Group", "inputAPIVersion": "Please enter the api version number", "model": "Model", "modelName": "Model Name", "name": "Name", "newChannel": "New Channel", "otherParameters": "Other Parameters", "priority": "Priority", "priorityWeightExplanation": "Priority/Weight Explanation:", "refreshClearSearchConditions": "Refresh/Clear Search Conditions", "replaceValue": "Replacement value", "responseTime": "Response Time", "search": "Search", "selectAll": "select all", "showAll": "Show All", "speedTestDisabled": "Speed Test Disabled", "status": "Status", "supplier": "Supplier", "tags": "Tags", "testAllChannels": "Test All Channels", "testModel": "Test Model", "type": "Type", "unselectAll": "Anti-select all", "updateEnabledBalance": "Update Enabled Balance", "usedBalance": "Used Balance/Remaining Balance", "weight": "Weight", "onlyTags": "Display tags only", "batchAddUserGroup": "Batch Add User Group", "batchAddUserGroupTip": "Select channels and add user groups. If a channel already has the group, it will be skipped.", "searchChannelPlaceholder": "Enter channel name to search (leave empty to get all channels)", "searchChannelLabel": "Search channels", "noMatchingChannels": "No matching channels found", "clickSearchToGetChannels": "Click search button to get channel list", "selectedChannelsCount": "Selected {{selected}} / {{total}} channels", "channelAlreadyHasGroup": "(Already has this group)", "currentGroup": "Current group", "noGroup": "None", "selectUserGroupToAdd": "Select user group to add", "pleaseSelectUserGroup": "Please select user group", "pleaseSelectChannelsForUserGroup": "Please select at least one channel", "addingUserGroup": "Adding...", "addUserGroupToChannels": "Add user group to {{count}} channels", "batchAddUserGroupSuccess": "Successfully added user group \"{{group}}\" to {{count}} channels"}, "channel_row": {"auto": "automatic", "canModels": "Available models:", "channelWeb": "Official website", "key": "Key", "keyRequired": "Key is required", "clickUpdateQuota": "Click to update balance", "delChannel": "Delete channel", "delChannelCount": "Removed all disabled channels, {{count}} in total", "delChannelInfo": "Whether to delete the channel", "delTag": "Delete tag", "delTagInfo1": "Whether to delete the label", "delTagInfo2": "⚠️ Note: This operation will delete the channel.", "manual": "Manual", "modelTestSuccess": "Channel {{channel}}: {{model}} tested successfully and took {{time}} seconds.", "modelTestTip": "Please set up the test model first", "onlyChat": "Only supports chat model", "otherArg": "Other parameters:", "priorityTip": "Priority cannot be less than 0", "proxy": "Agent address:", "test": "test", "testAllChannel": "Testing of all channels has been started successfully, please refresh the page to view the results.", "testModels": "Speed ​​test model:", "updateChannelBalance": "All enabled channel balances have been updated!", "updateOk": "update completed!", "weightTip": "Weight cannot be less than 1", "check": "Check", "batchAddIDRequired": "Please select at least one channel.", "batchDelete": "Batch delete", "batchDeleteConfirm": "Are you sure you want to delete the selected {{count}} channels? This action cannot be undone.", "batchDeleteError": "Batch deletion failed: {{message}}", "batchDeleteErrorTip": "Batch deletion error: {{message}}", "batchDeleteSuccess": "Batch deletion successful!", "batchDeleteTip": "Do you want to delete the selected channels in batch?", "deleteTag": "Delete tag", "deleteTagAndChannels": "Delete the tag and all its channels.", "deleteTagConfirm": "Are you sure you want to delete the tag {{tag}} and all its channels? This action cannot be undone.", "deleteTagError": "Failed to delete tag: {{message}}", "deleteTagSuccess": "Tag {{tag}} deleted successfully", "disable": "Disable", "disableAllChannels": "Disable all channels", "disableTagChannels": "Disable tag channel", "enable": "Enable", "enableAllChannels": "Enable all channels", "enableTagChannels": "Enable tag channel", "getTagChannelsError": "Failed to retrieve tag channel: {{message}}", "getTagChannelsErrorTip": "Error getting tag channel: {{message}}", "noTagChannels": "No tag channel found", "priorityUpdateError": "Priority update failed: {{message}}", "priorityUpdateSuccess": "Priority updated successfully", "refreshList": "Refresh list", "tag": "Tags", "tagChannelList": "Tag channel list", "tagChannelsConfirm": "Are you sure you want to {{action}} all channels under the tag {{tag}}?", "tagChannelsError": "Channel {{action}} failed: {{message}}", "tagChannelsSuccess": "The tag channel {{action}} is successful.", "weightUpdateError": "Weight update failed: {{message}}", "weightUpdateSuccess": "Weight update successful"}, "common": {"again": "Retry ({{count}})", "back": "return", "bindOk": "Binding successful!", "cancel": "Cancel", "close": "closure", "copyUrl": "Copy address", "create": "create", "currency": "USD", "delete": "delete", "deleteConfirm": "Delete {{title}}?", "disable": "disabled", "downImg": "Download pictures", "edit": "edit", "enable": "activated", "execute": "Would you like to execute {{title}}?", "executeConfirm": "execute", "exhaust": "exhausted", "expired": "expired", "imgUrl": "The map's address", "link": "Link", "loginOk": "login successful!", "newWindos": "open in a new window", "noData": "No data", "none": "none", "processing": "Processing...", "registerOk": "registration success!", "registerTip": "The verification code was sent successfully, please check your email!", "reset": "Reset", "saveSuccess": "Saved successfully!", "serverError": "Server Error", "show": "show", "submit": "submit", "unableServer": "Unable to connect to the server properly!", "unableServerTip": "New version available: {{version}}, please use the shortcut key Shift F5 to refresh the page", "unknown": "unknown", "verificationCode": "Verification code", "deleteError": "Deletion failed: {{message}}", "deleteSuccess": "Delete successful!", "jsonFormatError": "JSON format error", "actions": "Operation", "noDataAvailable": "No data available", "pageSize": "Number of items per page", "search": "Search", "total": "Total"}, "dashboard": "Dashboard", "dashboard_index": {"balance": "Balance", "calls": "Calls", "model_name": "Model Name", "model_price": "Available Models", "no_data": "No Data", "no_data_available": "No data available", "other_models": "Other Models", "statistics": "Statistics", "today_consumption": "Today's Consumption", "today_requests": "Today's Requests", "today_tokens": "Today's Tokens", "unknown": "Unknown", "used": "Used", "week_model_statistics": "Week Model Statistics", "week_consumption_log": "Week Consumption Log", "week_total_consumption": "Week Total Consumption", "7days_model_usage_pie": "7 Days Model Usage Pie", "date": "Date", "request_count": "Request Count", "amount": "Amount", "tokens": "Tokens(input/output)", "cache_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request_time": "Request Time(s)", "loading": "Loading...", "quickStart": "Quick Start", "quickStartTip": "When you click the button below, the system will automatically create a sys_playground token", "ComparedWithYesterday": "Compared with yesterday", "usage": "Usage", "RPM": "Current RPM", "TPM": "Current TPM", "total": "Total number", "availability": "Availability", "tab_status": "Status", "tab_dashboard": "Dashboard", "title": "Dashboard"}, "description": "All-in-one OpenAI interface\nIntegrates various API access methods\nOne-click deployment, ready to use", "echo 映射": "echo mapping", "error": {"unknownError": "unknown mistake"}, "fable 映射": "fable mapping", "footer": {"basedOn": "Based on", "developedBy": "Developed by", "license": "MIT License", "sourceCode": "Source code under"}, "home": {"loadingErr": "Failed to load homepage content..."}, "inviteCard": {"copyButton": {"copy": "Copy", "generate": "Generate"}, "generateInvite": "Generate Invite Link", "inviteDescription": "Share your invite link, invite friends to register, and get rewarded!", "inviteReward": "<PERSON><PERSON><PERSON>", "inviteUrlLabel": "Invite Link"}, "jump": "Redirecting...", "log": "Log", "invoice": "Monthly Bill", "midjourney": "Midjourney", "invoice_index": {"invoice": "Monthly Bill", "refresh": "Refresh", "id": "ID", "name": "Name", "status": "Status", "amount": "Amount", "createdTime": "Created Time", "paidTime": "Paid Time", "actions": "Actions", "deleteInvoice": "Delete Invoice", "confirmDeleteInvoice": "Are you sure you want to delete this invoice", "close": "Close", "delete": "Delete", "paid": "Paid", "unpaid": "Unpaid", "date": "Billing Time", "requestCount": "Total Requests", "quota": "Consumption Amount", "promptTokens": "Input Tokens", "completionTokens": "Output Tokens", "requestTime": "Request Time", "tokens": "Tokens (Input/Output)", "alert": "Monthly bill data is generated on the 1st of each month", "viewInvoice": "View Invoice", "modelName": "Model Name", "option": "Option", "username": "Username", "email": "Email", "userinfo": "User Information", "usage_statistics": "Usage Statistics", "download": "Download PDF", "usage_details": "Invoice Details", "summary": "Summary"}, "logPage": {"cachedTokens": "<PERSON><PERSON> (* {{ ratio }})", "channelLabel": "Channel", "columnSettings": "<PERSON>umn <PERSON>", "selectColumns": "Select Columns", "columnSelectAll": "Select All", "detailLabel": "Detail", "durationLabel": "Duration", "durationTooltip": "t/s: The number of output tokens divided by the total generation time, indicating the generation speed", "groupLabel": "Group", "inputAudioTokens": "Input audio tokens (* {{ ratio }})", "inputLabel": "Input", "inputTextTokens": "Input text tokens (* {{ ratio }})", "modelLabel": "Model", "outputAudioTokens": "Output audio tokens (* {{ ratio }})", "outputLabel": "Output", "outputTextTokens": "Output text tokens (* {{ ratio }})", "quotaLabel": "<PERSON><PERSON><PERSON>", "refreshButton": "Refresh/Clear Search", "searchButton": "Search", "searchLogsInfo": "Please check the recharge records and invitation records in the logs. For recharge records, select the type [Recharge] in the log; for invitation records, select [System] in the log.", "timeLabel": "Time", "title": "Logs", "tokenLabel": "Token", "totalInputTokens": "Total input tokens", "totalOutputTokens": "Total output tokens", "typeLabel": "Type", "userLabel": "User", "sourceIp": "Source IP", "unknown": "Unknown", "logType": {"all": "All", "recharge": "Recharge", "consumption": "Consumption", "management": "Management", "system": "System"}, "content": {"calculate_steps": "Calculation steps:", "calculate_steps_tip": "PS: This system calculates based on points, and all amounts are converted from points. 1 point = $0.000002, with a minimum spending of 1 point. This calculation is for reference only; actual charges may vary.", "channel_group": "Group: {{ channel_group }}", "group_discount": "Group discount: {{ discount }}", "input_price": "Input: ${{ price }} /M", "original_input_price": "Original input price: ${{ price }} /M", "original_output_price": "Original output price: ${{ price }} /M", "original_times_price": "Original price: ${{ times }} per time", "output_price": "Output: ${{ price }} /M", "times_price": "${{ times }} / time", "free": "Free", "old_log": "old version log", "illustrate": "* This record is the log recorded before the system upgrade"}, "quotaDetail": {"saved": "Saved ", "originalPrice": "Original Price", "inputPrice": "Input Price", "outputPrice": "Output Price", "groupRatio": "Group Ratio", "groupRatioValue": "Group Ratio", "actualPrice": "Actual Price", "input": "Input", "output": "Output", "finalCalculation": "Final Calculation", "originalBilling": "Original Billing", "actualBilling": "Actual Billing", "calculationNote": "PS: This system calculates based on points, and all amounts are converted from points. 1 point = $0.000002, with a minimum spending of 1 point. This calculation is for reference only; actual charges may vary.", "times": "times"}, "cachedReadTokens": "<PERSON><PERSON> read Tokens (* {{ ratio }})", "cachedWriteTokens": "Write tokens to cache (* {{ ratio }})", "reasoningTokens": "Deduction Tokens (* {{ ratio }})"}, "login": {"codeRequired": "verification code must be filled", "forgetPassword": "Forgot password?", "githubCountError": "An error occurred, retrying {{count}}...", "githubError": "The operation failed and redirected to the login interface...", "githubLogin": "GitHub login", "larkLogin": "<PERSON><PERSON><PERSON>", "oidcCountError": "An error occurred, retrying for the {{count}} time...", "oidcError": "Operation failed, redirecting to the login page...", "oidcLogin": "OIDC Login", "password": "Password", "passwordRequired": "Password is required", "passwordRest": "Password reset confirmation", "qrCode": "QR code", "useGithubLogin": "Log in using Github", "useLarkLogin": "Log in using Feishu", "useOIDCLogin": "Use OIDC Login", "useWechatLogin": "Log in with <PERSON><PERSON><PERSON>", "usernameOrEmail": "Username/Email", "usernameRequired": "Username/Email is required", "wechatLoginInfo": "Please use WeChat to scan the QR code to follow the official account and enter the \"verification code\" to obtain the verification code (valid within three minutes)", "wechatVerificationCodeLogin": "WeChat verification code login"}, "midjourneyPage": {"channel": "Channel", "failureReason": "Failure Reason", "midjourney": "Midjourney", "progress": "Progress", "prompt": "Prompt", "promptEn": "PromptEn", "refreshClearSearch": "Refresh / Clear Search Criteria", "resultImage": "Result Image", "search": "Search", "submissionResult": "Submission Result", "submitTime": "Submit Time", "taskID": "Task ID", "taskStatus": "Task Status", "timeConsuming": "time consuming", "type": "Type", "user": "User"}, "model_price": "Model Price", "modelpricePage": {"availableModels": "Available Models", "channelType": "Supplier", "group": "grouping", "inputMultiplier": "Input price", "model": "Model Name", "noneGroup": "The current group is not available.", "outputMultiplier": "Output price", "search": "Search", "times": "times", "tokens": "tokens", "type": "Type", "input_audio_tokens": "Audio input ratio", "other": "Other", "output_audio_tokens": "Audio output ratio", "rate": "Rate", "RPM": "API rate", "free": "Free", "cached_tokens": "<PERSON><PERSON>", "cached_write_tokens": "Cache write ratio", "cached_read_tokens": "<PERSON><PERSON>", "reasoning_tokens": "Reasoning Ratio", "input_text_tokens": "Input text Ratio", "output_text_tokens": "Output text Ratio", "all": "All", "extraRatios": "Expand price", "input": "Input", "input_image_tokens": "Enter image magnification", "noExtraRatios": "No additional charge", "output": "output", "output_image_tokens": "Output image magnification", "price": "Price", "showAll": "Show all", "onlyAvailable": "Only show available"}, "nova 映射": "nova mapping", "onyx 映射": "onyx mapping", "operation": "Operation", "orderlogPage": {"endTimeLabel": "End Time", "gatewayIdLabel": "Gateway ID", "gatewayNoLabel": "Gateway Order Number", "placeholder": {"gatewayId": "Gateway ID", "gatewayNo": "Gateway Order Number", "tradeNo": "Order Number", "userId": "User ID"}, "refreshClear": "Refresh/Clear Search Conditions", "search": "Search", "startTimeLabel": "Start Time", "statusLabel": "Status", "statusOptions": {"status1": "Status 1", "status2": "Status 2", "status3": "Status 3"}, "tableHeaders": {"amount": "Recharge Amount", "created_at": "Time", "discount": "Discount Amount", "fee": "Fee", "gateway_id": "Payment Gateway", "gateway_no": "Gateway Order Number", "order_amount": "Actual Payment Amount", "quota": "Credited Points", "status": "Status", "trade_no": "Order Number", "user_id": "User"}, "title": "Logs", "tradeNoLabel": "Order Number", "userIdLabel": "User ID"}, "paySetting": "Payment settings", "payment": "Payment", "paymentGatewayPage": {"createPayment": "Create Payment", "refreshClear": "Refresh/Clear Search Conditions", "search": "Search", "tableHeaders": {"action": "Action", "createdAt": "Created At", "enable": "Enable", "fixedFee": "Fixed Fee", "icon": "Icon", "id": "ID", "name": "Name", "percentFee": "<PERSON><PERSON>", "sort": "Sort", "type": "Type", "uuid": "UUID"}, "title": "Payment Gateway"}, "paymentPage": {"gatewaySettings": "Gateway Settings", "orderList": "Order List"}, "payment_edit": {"FixedTip": "A fixed handling fee is charged for each payment, in USD", "addOk": "Created successfully!", "currencyTip": "What currency does this gateway charge? Please check the corresponding gateway documentation.", "currencyType": "Gateway currency type", "notifyDomain": "Callback domain name", "notifyDomainTip": "The domain name for payment callback. Unless you have configured it yourself, keep it empty.", "paymentEdit": "Edit payment", "paymentType": "Payment Types", "percentTip": "Each payment is charged a handling fee based on a percentage. If it is 5%, please fill in 0.05", "requiredCurrency": "Currency cannot be empty", "requiredFixedFee": "Fixed handling fee cannot be less than 0", "requiredIcon": "Icon cannot be empty", "requiredPercentFee": "Percentage fee cannot be less than 0", "updateOk": "update completed!"}, "payment_row": {"delPayment": "Delete payment", "delPaymentTip": "Whether to delete payment", "sortTip": "Sort cannot be less than 0"}, "playground": "Playground", "pricing": "Pricing", "pricingPage": {"ModelCount": "Number of models", "currencyInfo1": "USD", "currencyInfo2": ": 1 === $0.002 / 1K tokens", "currencyInfo3": "CNY", "currencyInfo4": ": 1 === ￥0.014 / 1k tokens", "currencyInfo5": "Example", "currencyInfo6": ": gpt-4 Input: $0.03 / 1K tokens Completion: $0.06 / 1K tokens", "currencyInfo7": "0.03 / 0.002 = 15, 0.06 / 0.002 = 30, input multiplier is 15, completion multiplier is 30", "errPricesWarning": "There are models with incorrect supplier types, please configure them in time:", "multipleOperation": "Multiple Operation", "newButton": "New", "noPriceModelWarning": "There are models without configured prices, please configure the prices in time:", "refreshButton": "Refresh", "singleOperation": "Single Operation", "updatePricesButton": "Update Prices", "title": "Model price"}, "pricing_edit": {"channelType": "Channel type", "channelTypeErr": "Channel type error", "channelTypeErr2": "The channel type is wrong", "delGroup": "Delete price group", "delGroupTip": "Delete price group?", "delInfoTip": "Are you sure you want to delete {{name}}?", "delTip": "confirm delete?", "inputVal": "The input magnification must be greater than or equal to 0", "model": "Model", "modelNameRe": "Model names cannot be repeated", "modelTip": "Please select the model supported by the price. You can also enter the wildcard character * to match the model, for example: gpt-3.5*, which means that all models starting with gpt-3.5 are supported. The * sign can only be used in the last digit, and there must be characters in front of it. \n, for example: gpt-3.5* is correct, *gpt-3.5 is wrong", "name": "name", "outputVal": "The output magnification must be greater than or equal to 0", "requiredChannelType": "Channel type cannot be empty", "requiredInput": "Input magnification cannot be empty", "requiredModelName": "Model name cannot be empty", "requiredModels": "Model cannot be empty", "requiredOutput": "Output magnification cannot be empty", "requiredType": "Type cannot be empty", "saveOk": "Saved successfully", "type": "type", "typeCheck": "The type can only be tokens or times", "typeErr": "type error", "locked_title": "Lock price", "locked": "Locked price", "unlocked": "Unlocked price", "lockedTip": "Price locked cannot be overridden by price updates on the server or program", "completionRatio": "Completion ratio", "delMultipleInfoTip": "Are you sure you want to delete the selected {{count}} prices?", "extraRatios": "Extra expansion factor", "noAvailableRatios": "No available extension ratio", "noExtraRatios": "No additional magnification rate is available, please select to add from the drop-down menu.", "promptRatio": "Leverage ratio", "selectExtraRatio": "Select magnification ratio"}, "profile": "Profile", "profilePage": {"accountBinding": "Account Binding", "bindEmail": "Bind <PERSON><PERSON>", "bindGitHubAccount": "Bind <PERSON><PERSON><PERSON><PERSON> Account", "bindLarkAccount": "Bind Lark Account", "bindWechatAccount": "Bind <PERSON><PERSON><PERSON> Account", "changeEmail": "Change Email", "displayName": "Display Name", "generateToken": "Generate Access Token", "inputDisplayNamePlaceholder": "Enter display name", "inputPasswordPlaceholder": "Enter password", "inputUsernamePlaceholder": "Enter username", "keepSafe": "Keep it safe. Reset immediately if leaked.", "lark": "Lark", "notBound": "Not Bound", "other": "Other", "password": "Password", "passwordMinLength": "Password must be at least 8 characters long", "personalInfo": "Personal Information", "resetToken": "Reset Access Token", "submit": "Submit", "telegramBot": "Telegram bot", "telegramStep1": "1. Click the button below, the robot will open in Telegram, click /start to start.", "telegramStep2": "2. After sending the /bind command to the robot, enter the access token below to bind. \n(If it is not generated, please click the button below to generate it)", "token": "Token", "tokenNotice": "Note: Tokens generated here are for system management, not for accessing OpenAI services.", "updateSuccess": "User information updated successfully!", "username": "Username", "usernameMinLength": "Username must be at least 3 characters long", "usernameRequired": "Username cannot be empty", "wechatBindSuccess": "WeChat account successfully bound!", "yourTokenIs": "Your access token is:"}, "redemption": "Redemption", "redemptionPage": {"createRedemptionCode": "Create Redemption Code", "del": "Delete redemption code", "delTip": "Whether to delete the redemption code", "headLabels": {"action": "Action", "createdTime": "Created Time", "id": "ID", "name": "Name", "quota": "<PERSON><PERSON><PERSON>", "redeemedTime": "Redeemed Time", "status": "Status"}, "pageTitle": "Redemption Management", "refreshButton": "Refresh", "searchPlaceholder": "Search redemption code...", "successMessage": "Operation successful", "unredeemed": "Not yet redeemed"}, "redemption_edit": {"addOk": "Redemption code created successfully!", "editOk": "Redemption code updated successfully!", "number": "quantity", "requiredCount": "Must be greater than or equal to 1", "requiredQuota": "Must be greater than or equal to 0"}, "registerForm": {"confirmPasswordRequired": "Confirm password is required", "emailRequired": "Email is required", "enterEmail": "Please enter your email", "getCode": "Get verification code", "passwordRequired": "Password is required", "passwordsNotMatch": "Passwords do not match", "resendCode": "Resend({{countdown}})", "restSendEmail": "The reset email was sent successfully, please check your email!", "turnstileError": "Please try again in a few seconds, Turnstile is checking the user environment!", "usernameRequired": "Username is required", "validEmailRequired": "Must be a valid email address", "verificationCodeRequired": "Verification code is required", "verificationInfo": "Please try again in a few seconds, Turnstile is checking the user environment!"}, "registerPage": {"alreadyHaveAccount": "Already have an account? Click to login"}, "res_time": {"lastTime": "Last speed test time:", "noTest": "Not tested", "second": "Second", "testClick": "Click speed test (only supports chat model)"}, "setting": "Setting", "theme": {"auto": "Follow System", "light": "Light Mode", "dark": "Dark Mode"}, "setting_index": {"operationSettings": {"chatLinkSettings": {"info": "Configure chat links, which take effect in the chat of the token and in the chat of the Playground on the homepage. <br />In the link, &#123;key&#125; can replace the user's token, and &#123;server&#125; can replace the server address. For example: {'https://chat.oneapi.pro/#/?settings={\"key\":\"sk-{key}\",\"url\":\"{server}\"}'}<br />If not configured, the following 4 links will be configured by default:<br />ChatGPT Next ： {'https://chat.oneapi.pro/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />chatgpt-web-midjourney-proxy ： {'https://vercel.ddaiai.com/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />AMA ： {'ama://set-api-key?server={server}&key={key}'}<br />opencat ： {'opencat://team/join?domain={server}&token={key}'}<br />Sorting rules: The larger the value, the higher the ranking, and the same value is sorted according to the configuration order", "save": "Save Chat Link Settings", "title": "Chat Link Settings"}, "generalSettings": {"approximateToken": "Use approximate method to estimate token count to reduce computation", "chatLink": {"label": "Chat Link", "placeholder": "For example, the deployment address of ChatGPT Next Web"}, "displayInCurrency": "Display quota in currency", "displayTokenStat": "Show token quota instead of user quota for billing related APIs", "quotaPerUnit": {"label": "Quota Per Unit", "placeholder": "The quota that can be exchanged for one unit of currency"}, "retryCooldownSeconds": {"label": "Retry Cooldown (seconds)", "placeholder": "Retry cooldown (seconds)"}, "retryTimes": {"label": "Retry Times", "placeholder": "Number of retries"}, "saveButton": "Save General Set<PERSON>s", "title": "General Settings", "topUpLink": {"label": "Top-up Link", "placeholder": "For example, the purchase link of the card issuing website"}, "retryTimeOut": {"label": "Retry Timeout (seconds)", "placeholder": "Retry Timeout (seconds)"}, "emptyResponseBilling": "Empty Response Billing", "unifiedRequestResponseModel": "Unified Request Response Model", "unifiedRequestResponseModelTooltip": "When model mapping exists, the model value in the response will be the requested model value instead of the actual called model name"}, "logSettings": {"clearLogs": "Clear Historical Logs", "logCleanupTime": {"label": "Log Cleanup Time", "placeholder": "Log Cleanup Time"}, "logConsume": "Enable Log Consumption", "title": "Log Settings"}, "monitoringSettings": {"automaticDisableChannel": "Automatically Disable Channel on Failure", "automaticEnableChannel": "Automatically Enable Channel on Success", "channelDisableThreshold": {"label": "Maximum Response Time", "placeholder": "In seconds, if all running channels exceed this time, the channel will be automatically disabled"}, "quotaRemindThreshold": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "When below this quota, an email will be sent to remind the user"}, "saveMonitoringSettings": "Save Monitoring Settings", "title": "Monitoring Settings"}, "otherSettings": {"CFWorkerImageUrl": {"alert": "Here is the image proxy address of Cloudflare Worker. You can use it by deploying https://github.com/MartialBE/get-image-by-cf. You can set only one of it and the image detection proxy. \nNote that some image links may deny CF access and cause detection failure.", "key": "Cloudflare Worker image proxy key, please ignore if not configured", "label": "Cloudflare Worker Image Proxy"}, "alert": "When users use the vision model and provide an image link, our server needs to download these images and calculate tokens. To protect the server's IP address from being exposed when downloading images, you can configure a proxy below. This proxy configuration uses HTTP or SOCKS5 proxy. If you are an individual user, you can ignore this configuration. The proxy format is http://127.0.0.1:1080 or socks5://127.0.0.1:1080", "chatImageRequestProxy": {"label": "Image Detection Proxy", "placeholder": "Chat image detection proxy settings, may leak server IP if not set"}, "mjNotify": "Midjourney allows callbacks (will expose server IP address)", "saveButton": "Save Other Settings", "title": "Other Settings", "claudeAPIEnabled": "Enable Claude API?", "geminiAPIEnabled": "Enable Gemini API?"}, "paymentSettings": {"alert": "Payment Settings: <br />1. USD Exchange Rate: Used to calculate the amount of recharge in USD <br />2. Minimum Recharge Amount (USD): Minimum recharge amount, in USD, enter an integer <br />3. All pages are calculated in USD, and the actual currency paid by the user is converted according to the currency set by the payment gateway <br />For example: A gateway sets the currency as CNY, the user pays 100 USD, then the actual payment amount is 100 * USD exchange rate <br />B gateway sets the currency as USD, the user pays 100 USD, then the actual payment amount is 100 USD", "discount": {"label": "Fixed Amount Recharge Discount", "placeholder": "It is a JSON text, the key is the recharge amount, and the value is the discount"}, "discountInfo": "Fixed Amount Recharge Discount Example: <br />It is a JSON text, the key is the recharge amount, and the value is the discount. For example, &#123;&quot;10&quot;:0.9&#125; means that a recharge of 10 USD is calculated at a 10% discount <br />Calculation formula: actual cost = (original value * discount + original value * discount * fee rate) * exchange rate", "minAmount": {"label": "Minimum Recharge Amount (USD)", "placeholder": "e.g., 1, then the minimum recharge amount is 1 USD, please enter an integer"}, "save": "Save Payment Settings", "title": "Payment Settings", "usdRate": {"label": "USD Exchange Rate", "placeholder": "e.g., 7.3"}}, "quotaSettings": {"preConsumedQuota": {"label": "Pre-consumed Quota for Requests", "placeholder": "Adjustable after the request ends"}, "quotaForInvitee": {"label": "<PERSON><PERSON> for Invitee", "placeholder": "e.g., 1000"}, "quotaForInviter": {"label": "<PERSON><PERSON> for Inviter", "placeholder": "e.g., 2000"}, "rechargeRewardType": {"label": "Recharge Reward Type", "fixed": "Fixed", "percentage": "Percentage"}, "rechargeRewardValue": {"label": "Recharge Reward Value", "fixedPlaceholder": "e.g., 2000", "percentagePlaceholder": "e.g., 10"}, "quotaForNewUser": {"label": "Initial Quota for New Users", "placeholder": "e.g., 100"}, "saveQuotaSettings": "Save <PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>"}, "rateSettings": {"groupRatio": {"label": "Group Ratio", "placeholder": "It is a JSON text, the key is the group name, and the value is the ratio"}, "save": "Save Rate Settings", "title": "Rate Settings"}, "title": "Operation Settings", "extraTokenPriceJson": {"info": "Configure extra token prices. The configuration format is JSON, with keys as model names and values as input-output token prices. For example: {\"gpt-4o-audio-preview\": {\"input_audio_tokens_ratio\": 40, \"output_audio_tokens_ratio\": 20}, \"gpt-4o-mini-audio-preview\": {\"input_audio_tokens_ratio\": 67, \"output_audio_tokens_ratio\": 34}}", "save": "Save extra token price settings", "title": "Extra token price settings"}, "disableChannelKeywordsSettings": {"info": "Disable channel keywords, one keyword per line.", "save": "Save disabled channel keyword settings", "title": "Disable channel keyword settings"}, "safetySettings": {"title": "System Safety Settings", "enableSafe": "Enable Prompt Safety Check", "safeToolName": {"label": "Safe Check Tool"}, "safeKeyWords": {"label": "Keyword List", "placeholder": "Enter keywords, one per line"}, "save": "Save Settings"}, "claudeSettings": {"budgetTokensPercentage": {"label": "Default thinking token percentage", "placeholder": "Please enter the default thinking token percentage."}, "defaultMaxTokens": {"label": "Default MaxToken quantity", "placeholder": "Please enter the default MaxToken quantity in JSON format, where \"default\" represents the default value. For example: {\"default\": 1000, \"claude-3-7-sonnet-latest\": 128000}"}, "save": "Save <PERSON>'s settings"}, "geminiSettings": {"title": "Gemini Settings", "geminiOpenThink": {"label": "Models with reasoning output enabled", "placeholder": "Please enter the models that need reasoning output enabled in JSON format, for example: {\"gemini-2.5-pro-preview-05-06\": true}"}, "save": "Save Gemini Settings"}}, "otherSettings": {"customSettings": {"aboutLabel": "About", "aboutPlaceholder": "Enter new about content here, supports Markdown & HTML code. If a link is entered, it will be used as the src attribute of the iframe, allowing you to set any web page as the about page.", "copyrightWarning": "Removing the Done Hub copyright must be authorized first. Maintaining the project requires a lot of effort. If this project is meaningful to you, please support it.", "footerLabel": "<PERSON><PERSON>s", "footerPlaceholder": "Enter new footer here, leave blank to use the default footer, supports HTML code", "homePageContentLabel": "Home Page Content", "homePageContentPlaceholder": "Enter home page content here, supports Markdown & HTML code. Once set, the home page status information will no longer be displayed. If a link is entered, it will be used as the src attribute of the iframe, allowing you to set any web page as the home page.", "logoLabel": "Logo Image URL", "logoPlaceholder": "Enter Logo Image URL here", "saveAbout": "Save About", "saveHomePageContent": "Save Home Page Content", "setFooter": "<PERSON>", "setLogo": "Set <PERSON>", "setSystemName": "Set System Name", "systemNameLabel": "System Name", "systemNamePlaceholder": "Enter system name here", "title": "Personalization Settings"}, "generalSettings": {"checkUpdate": "Check Update", "currentVersion": "Current Version", "noticeLabel": "Notice", "noticePlaceholder": "Enter new notice content here, supports Markdown & HTML code", "saveNotice": "Save Notice", "title": "General Settings"}, "title": "Other Settings", "updateDialog": {"close": "Close", "newVersion": "New Version", "viewGitHub": "View on GitHub"}}, "systemSettings": {"configureEmailDomainWhitelist": {"allowedEmailDomains": "Allowed Email Domains", "emailDomainRestriction": "Enable Email Domain Whitelist", "save": "Save Email Domain Whitelist Settings", "subTitle": "Prevent malicious users from registering with temporary email addresses", "title": "Configure Email Domain Whitelist"}, "configureFeishuAuthorization": {"alert1": "Enter Homepage URL", "alert2": "and Redirect URL", "appId": "App ID", "appIdPlaceholder": "Enter App ID", "appSecret": "App Secret", "appSecretPlaceholder": "Sensitive information will not be sent to the frontend", "manage": "Manage your Feishu app", "manageLink": "Click here", "saveButton": "Save <PERSON><PERSON><PERSON>", "subTitle": "To support login and registration via Feishu.", "title": "Configure Feishu Authorization"}, "configureGitHubOAuthApp": {"alert1": "Enter Homepage URL", "alert2": "and Authorization callback URL", "clientId": "GitHub Client ID", "clientIdPlaceholder": "Enter the ID of your registered GitHub OAuth App", "clientSecret": "GitHub Client Secret", "clientSecretPlaceholder": "Sensitive information will not be sent to the frontend", "manage": "Manage your GitHub OAuth App", "manageLink": "Click here", "saveButton": "Save GitHub OAuth Settings", "subTitle": "To support login and registration via GitHub.", "title": "Configure GitHub OAuth App"}, "configureLoginRegister": {"emailVerification": "Email Verification Required for Password Register", "gitHubOAuth": "Allow Login & Register via GitHub", "gitHubOldIdClose": "Disable logging in with old GitHub ID.", "larkAuth": "Allow Login & Register via Lark", "oidcAuth": "Enable login & registration via OIDC account", "passwordLogin": "Allow Password Login", "passwordRegister": "Allow Password Register", "registerEnabled": "Allow New User Registration (Disabling this will prevent any new registrations)", "title": "Configure Login and Register", "turnstileCheck": "Enable Turnstile User Verification", "weChatAuth": "Allow Login & Register via WeChat"}, "configureOIDCAuthorization": {"alert1": "Fill in the homepage link", "alert2": ", fill in the redirect URL", "clientId": "Client ID (Client ID)", "clientIdPlaceholder": "Please enter Client ID", "clientSecret": "Client Secret", "clientSecretPlaceholder": "Sensitive information will not be displayed on the frontend", "issuer": "OIDC Issuer", "issuerPlaceholder": "Please enter OIDC Issuer", "saveButton": "Save OIDC Settings", "scopes": "<PERSON><PERSON><PERSON>", "scopesPlaceholder": "Enter scopes (separated by commas, typically 'openid,email,profile')", "subTitle": "Used to configure standard OIDC authorization login system", "title": "Configure OIDC Single Authorization System", "usernameClaims": "<PERSON><PERSON><PERSON>", "usernameClaimsPlaceholder": "Enter username claim (e.g. username)"}, "configureSMTP": {"alert": "Please note, some email providers include your server IP address in sent emails. For non-personal use, consider using a professional email service provider.", "save": "Save SMTP Settings", "smtpAccount": "SMTP Account", "smtpAccountPlaceholder": "Usually your email address", "smtpFrom": "SMTP Sender Email", "smtpFromPlaceholder": "Usually matches your email address", "smtpPort": "SMTP Port", "smtpPortPlaceholder": "Default: 587", "smtpServer": "SMTP Server Address", "smtpServerPlaceholder": "e.g., smtp.qq.com", "smtpToken": "SMTP Access Token", "smtpTokenPlaceholder": "Sensitive information will not be sent to the frontend", "subTitle": "Support system email sending", "title": "Configure SMTP"}, "configureTurnstile": {"manage": "Manage your Turnstile Sites, recommend selecting Invisible Widget Type.", "manageLink": "Click here", "saveButton": "Save Turn<PERSON><PERSON> Settings", "secretKey": "Turnstile Secret Key", "secretKeyPlaceholder": "Sensitive information will not be sent to the frontend", "siteKey": "Turnstile Site Key", "siteKeyPlaceholder": "Enter your registered Turnstile Site Key", "subTitle": "To support user verification.", "title": "Configure Turn<PERSON><PERSON>"}, "configureWeChatServer": {"accessToken": "WeChat Server Access Token", "accessTokenPlaceholder": "Sensitive information will not be sent to the frontend", "learn": "Learn about WeChat Server", "learnLink": "Click here", "qrCodeImage": "WeChat Official Account QR Code Image Link", "qrCodeImagePlaceholder": "Enter an image link", "saveButton": "Save WeChat Server Settings", "serverAddress": "WeChat Server Server Address", "serverAddressPlaceholder": "e.g., https://yourdomain.com", "subTitle": "To support login and registration via WeChat.", "title": "Configure WeChat Server"}, "generalSettings": {"serverAddress": "Server Address", "serverAddressPlaceholder": "e.g., https://yourdomain.com", "title": "System Settings", "updateServerAddress": "Update Server Address"}, "title": "System Settings"}}, "shimmer 映射": "shimmer mapping", "suno": {"lyrics": "lyrics", "music": "Audio", "response": "response body", "video": "video"}, "tableToolBar": {"channelId": "Channel ID", "channelIdPlaceholder": "Channel ID", "endTime": "End Time", "modelName": "Model Name", "startTime": "Start Time", "taskId": "Task ID", "taskIdPlaceholder": "Task ID", "tokenName": "Token Name", "type": "Type", "username": "Username", "sourceIp": "Source IP"}, "task": "Asynchronous tasks", "taskPage": {"channel": "channel", "fail": "Reason for failure", "finishTime": "Complete time", "platform": "platform", "progress": "schedule", "status": "Task status (click to view results)", "subTime": "Submission time", "task": "Task ID", "time": "time consuming", "type": "type", "user": "user", "title": "Asynchronous task"}, "telegramPage": {"action": "Action", "command": "Command", "createMenu": "Create", "description": "Description", "id": "ID", "infoMessage": "After adding or modifying menu commands/descriptions (you do not need to reload if you have not modified commands and descriptions), you need to reload the menu to take effect. If you do not see the new menu, try killing the backend and restarting the program.", "offline": "Offline(Polling)", "online": "Online(Webhook)", "operationSuccess": "Operation successfully completed!", "refresh": "Refresh", "reloadMenu": "Reload Menu", "reloadSuccess": "Reload successful!", "replyContent": "Reply Content", "replyType": "Reply Type", "searchPlaceholder": "Search ID and command...", "title": "Telegram Bot Menu"}, "telegram_edit": {"addOk": "<PERSON><PERSON> created successfully!", "msgInfo": "Message content", "msgType": "Message type", "requiredCommand": "command cannot be empty", "requiredDes": "Description cannot be empty", "requiredMes": "Message content cannot be empty", "requiredParseMode": "Message type cannot be empty", "updateOk": "Menu updated successfully!"}, "token": "Token", "token_index": {"actions": "Actions", "cancel": "Cancel", "chat": "Cha<PERSON>", "close": "Close", "confirmDeleteToken": "Are you sure you want to delete this token?", "copy": "Copy", "createToken": "Create Token", "createdTime": "Created Time", "delete": "Delete", "deleteToken": "Delete Token", "editToken": "<PERSON>", "enableCache": "Enable Cache (Enabling this will cache chat history to reduce cost)", "expiryTime": "Expiry Time", "invalidDate": "Invalid date", "name": "Name", "neverExpires": "Never Expires", "quota": "<PERSON><PERSON><PERSON>", "quotaNote": "Note: The token's quota is only used to limit the maximum usage of the token itself, actual usage is subject to the remaining quota of the account.", "refresh": "Refresh", "remainingQuota": "<PERSON><PERSON><PERSON>", "replaceApiAddress1": "Replace the OpenAI API base address https://api.openai.com with", "replaceApiAddress2": ", and you can use the key below.", "searchTokenName": "Search for token name...", "status": "Status", "submit": "Submit", "token": "Token", "unlimited": "Unlimited", "unlimitedQuota": "Unlimited <PERSON><PERSON>ta", "usedQuota": "Used Quota", "userGroup": "group", "apiRate": "", "apiRateTip": "", "heartbeat": "Heartbeat setting (Experimental)", "heartbeatTip": "Heartbeat setting means that when you make a stream request, if there is no response for a long time, your client may disconnect due to the timeout mechanism. To prevent this, you can enable the heartbeat setting. When the request exceeds the start time you set and there is no response, we will send a heartbeat request every 5 seconds to keep the connection. Note: If you are using a relay program, please do not enable this setting, it may cause unexpected issues.", "heartbeatTimeout": "Heartbeat start time (unit: seconds)", "heartbeatTimeoutHelperText": "Minimum value: 30 seconds, maximum value: 90 seconds"}, "topup": "Top-up", "topupCard": {"actualAmountToPay": "Actual amount to pay", "adminSetupRequired": "Admin setup required!", "amount": "Top up amount", "amountMaxLimit": "Amount cannot exceed 1000000", "amountMinLimit": "Amount cannot be less than", "currentQuota": "Current quota", "discountedPrice": "Discounted price", "exchangeButton": {"default": "Exchange", "submitting": "Submitting"}, "exchangeRate": "Exchange rate", "fee": "Fee", "getRedemptionCode": "Get redemption code", "inputLabel": "Redemption code", "inputPlaceholder": "Enter redemption code!", "noRedemptionCodeText": "No redemption code yet? Click to get redemption code:", "onlineTopup": "Online top-up", "positiveIntegerAmount": "Please enter a positive integer amount", "redemptionCodeTopup": "Redemption code top-up", "selectPaymentMethod": "Select payment method", "topup": "Top up", "topupAmount": "Top up amount", "topupsuccess": "Top up successful!"}, "topupPage": {"alertMessage": "Please check the recharge records and invitation records in the logs. For recharge records, select the type [Recharge] in the log; for invitation records, select [System] in the log."}, "ui-component": {"allModels": "View all models", "modelName": "Model name"}, "user": "User", "userGroup": {"apiRate": "API rate", "apiRateTip": "The number of requests allowed per minute. When the rate is less than 60, use a counter limiter; when the rate is greater than or equal to 60, use a token bucket limiter. This setting is only effective when Redis is enabled.", "create": "Create new group", "enable": "Enable or not", "id": "ID", "name": "Name", "nameTip": "Name for users", "public": "Is it public?", "promotion": "Auto Upgrade", "promotionTip": "When enabled, users will automatically upgrade to this group when their recharge amount meets the min-max conditions.", "min": "<PERSON>", "minTip": "Minimum recharge amount required for auto upgrade.", "max": "<PERSON>", "maxTip": "Maximum recharge amount for this group level.", "ratio": "magnification", "symbol": "Identification", "symbolTip": "The label is used to distinguish user groups, please use English and do not repeat.", "title": "User grouping"}, "userPage": {"action": "Action", "adminUserRole": "administrator", "bind": "Bind", "cUserRole": "general user", "cancel": "Cancel", "cancelAdmin": "Cancel administrator", "createUser": "Create User", "creationTime": "Creation Time", "del": "delete users", "delTip": "Whether to delete the user", "displayName": "Display Name", "editUser": "Edit User", "group": "Group", "groupRequired": "Group is required", "id": "ID", "operationSuccess": "Operation successfully completed!", "password": "Password", "passwordRequired": "Password is required", "quota": "<PERSON><PERSON><PERSON>", "quotaMin": "Quota cannot be less than 0", "refresh": "Refresh", "saveSuccess": "Saved successfully!", "searchPlaceholder": "Search for user ID, username, group, display name, or email address...", "setAdmin": "Set as administrator", "statistics": "Statistics", "status": "Status", "submit": "Submit", "superAdminRole": "super administrator", "uUserRole": "Unknown identity", "useQuota": "Number of requests", "userRole": "User Role", "username": "Username", "usernameRequired": "Username is required", "users": "Users", "changeQuota": "Change quota", "changeQuotaHelperText": "Here is an increase or decrease, not a direct change to the user's balance. Enter dollars, you can deduct up to {{quota}} at most.", "changeQuotaNotEmpty": "Please fill in the change amount.", "changeQuotaNotEnough": "Cannot deduct an amount exceeding the user's balance.", "quotaRemark": "Remark"}, "user_group": "User grouping", "validation": {"requiredName": "Name is required"}, "仅支持聊天": "Chat only", "从Cohere获取模型列表": "Get list of models from Cohere", "从Deepseek获取模型列表": "Get model list from Deepseek", "从Gemini获取模型列表": "Get model list from Gemini", "从Groq获取模型列表": "Get list of models from Groq", "从Mistral获取模型列表": "Get model list from Mistral", "从Moonshot获取模型列表": "Get model list from Moonshot", "从OpenAI获取模型列表": "Get model list from OpenAI", "从渠道获取模型列表": "Get model list from channel", "代理地址": "proxy address", "代码执行": "code execution", "位置/区域": "location/area", "你可以为你的渠道打一个标签，打完标签后，可以通过标签进行批量管理渠道，注意：设置标签后某些设置只能通过渠道标签修改，无法在渠道列表中修改。": "You can tag your channel. After tagging, you can manage channels in batches through tags. Note: after setting tags, some settings can only be modified through channel tags and cannot be changed in the channel list.", "使用代码执行功能，开启后，计算tokens不准确，建议个人使用开启": "When using the code execution function, the calculation of tokens is inaccurate after it is turned on. It is recommended that individuals use it to turn it on.", "使用网页搜索功能，对用户输入的内容进行搜索": "Use the web search function to search for content entered by the user", "其他参数": "Other parameters", "可空，请输入中转API地址，例如通过cloudflare中转": "Can be left empty, please enter the transfer API address, for example, through cloudflare transfer. Supports {model} variable, e.g.: https://api.example.com/v1/{model}/chat", "启用": "enable", "地址填写Suno-API部署的地址": "Address: Fill in the address of Suno-API deployment", "地址填写midjourney-proxy部署的地址": "Address: fill in the address of midjourney-proxy deployment", "声音映射": "sound mapping", "如果选择了仅支持聊天，那么遇到有函数调用的请求会跳过该渠道": "If you choose to support chat only, the channel will be skipped when encountering requests with function calls.", "密钥": "key", "密钥填写Suno-API的密钥，如果没有设置密钥，可以随便填": "Fill in the key of Suno-API for the key. If there is no key set, you can fill it in casually.", "密钥填写midjourney-proxy的密钥，如果没有设置密钥，可以随便填": "The key is the key of midjourney-proxy. If the key is not set, you can fill it in casually.", "将OpenAI的声音角色映射到azure的声音角色, 如果有role，请用|隔开，例如zh-CN-YunxiNeural|boy": "Map OpenAI's voice role to <PERSON><PERSON>'s voice role. If there is a role, please separate it with |, for example, zh-CN-YunxiNeural|boy", "当涉及到知识库ID时，请前往开放平台的知识库模块进行创建或获取(是知识库ID不是文档ID！)": "When it comes to the knowledge base ID, please go to the knowledge base module of the open platform to create or obtain it (it is the knowledge base ID, not the document ID!)", "必须填写所有数据后才能获取模型列表": "All data must be filled in to get the model list", "按照如下格式输入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041": "Enter in the following format: APIKey-AppId, for example: fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041", "按照如下格式输入：APIKey|SecretKey": "Enter according to the following format: APIKey|SecretKey", "按照如下格式输入：APISecret|groupID": "Enter it in the following format: APISecret|groupID", "按照如下格式输入：APPID|APISecret|APIKey": "Enter it in the following format: APPID|APISecret|APIKey", "按照如下格式输入：AppId|SecretId|SecretKey": "Enter it in the following format: AppId|SecretId|SecretKey", "按照如下格式输入：CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN": "Enter it in the following format: CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN", "按照如下格式输入：Region|AccessKeyID|SecretAccessKey|SessionToken 其中SessionToken可不填空": "Enter it in the following format: Region|AccessKeyID|SecretAccessKey|SessionToken. SessionToken can be left blank.", "按照如下格式输入：SecretId|SecretKey": "Enter it in the following format: SecretId|SecretKey", "插件参数": "Plug-in parameters", "是否启用代码执行": "Whether to enable code execution", "是否启用网页搜索": "Whether to enable web search", "替换 API 版本": "Replace API version", "本配置主要是用于使用cloudflare Zero Trust将端口暴露到公网时，需要配置的header": "This configuration is mainly used for the headers that need to be configured when using cloudflare Zero Trust to expose the port to the public network.", "标签": "Label", "模型": "Model", "模型名称为coze-{bot_id}，你也可以直接使用 coze-* 通配符来匹配所有coze开头的模型": "The model name is coze-{bot_id}. You can also directly use coze-* wildcards to match all models starting with coze.", "模型名称映射， 你可以取一个容易记忆的名字来代替coze-{bot_id}，例如：{\"coze-translate\": \"coze-xxxxx\"},注意：如果使用了模型映射，那么上面的模型名称必须使用映射前的名称，上述例子中，你应该在模型中填入coze-translate(如果已经使用了coze-*，可以忽略)。": "Model name mapping, you can replace coze-{bot_id} with an easy-to-remember name, for example: {\"coze-translate\": \"coze-xxxxx\"}. Note: If model mapping is used, the above model name must \nUse the name before mapping. In the above example, you should fill in coze-translate in the model (you can ignore it if coze-* is already used).", "模型映射关系": "Model mapping relationship", "测速模型": "speed model", "渠道API地址": "Channel API address", "渠道名称": "Channel name", "渠道类型": "Channel type", "版本号": "version number", "用于测试使用的模型，为空时无法测速,如：gpt-3.5-turbo，仅支持chat模型": "The model used for testing cannot measure speed when empty. For example: gpt-3.5-turbo, only supports chat model.", "用户组": "user group", "知识库": "knowledge base", "知识库ID": "Knowledge base ID", "知识库模板": "Knowledge base template", "网页搜索": "web search", "请为渠道命名": "Please name the channel", "请前往开放平台的知识库上传文档，然后使用知识库功能进行检索。": "Please go to the knowledge base of the open platform to upload documents, and then use the knowledge base function to search.", "请参考wiki中的文档获取key. https://github.com/MartialBE/one-hub/wiki/VertexAI": "Please refer to the documentation in the wiki to obtain the key. https://github.com/MartialBE/one-hub/wiki/VertexAI", "请填写AZURE_OPENAI_ENDPOINT": "Please fill in AZURE_OPENAI_ENDPOINT", "请求模型时的知识库模板, 请查看文档填写，否则不用填写": "Knowledge base template when requesting a model, please check the document to fill it in, otherwise there is no need to fill it in", "请输入你 Speech Studio 的位置/区域，例如：eastasia": "Please enter the location/region of your Speech Studio, for example: eastasia", "请输入你部署的Ollama地址，例如：http://127.0.0.1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": "Please enter the Ollama address you deployed, for example: http://127.0.0.1:11434. If you use cloudflare Zero Trust, you can fill in the authorization information in the plug-in below.", "请输入插件参数，即 X-DashScope-Plugin 请求头的取值": "Please enter the plug-in parameters, that is, the value of the X-DashScope-Plugin request header", "请输入渠道对应的鉴权密钥": "Please enter the authentication key corresponding to the channel", "请输入版本号，例如：v1": "Please enter the version number, for example: v1", "请输入默认API版本，例如：2024-05-01-preview": "Please enter the default API version, for example: 2024-05-01-preview", "请选择渠道类型": "Please select channel type", "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的": "Please select the model supported by this channel. You can also enter the wildcard character * to match the model. For example: gpt-3.5*, which means that all models starting with gpt-3.5 are supported. The * sign can only be used in the last digit, and there must be characters in front of it. \n, for example: gpt-3.5* is correct, *gpt-3.5 is wrong", "请选择该渠道所支持的用户组": "Please select the user group supported by this channel", "请随意填写": "Please feel free to fill in", "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx.com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "After input, v1 in the request address will be replaced. For example: freeapi, when requesting chat, it will become https://xxx.com/freeapi/chat/completions. If you need to disable the version number, please enter disable", "这里选择预计费选项，用于预估费用，如果你觉得计算图片占用太多资源，可以选择关闭图片计费。但是请注意：有些渠道在stream下是不会返回tokens的，这会导致输入tokens计算错误。": "Select the estimated cost option here for cost estimation. If you think that calculating images consumes too many resources, you can choose to disable image billing. However, please note: some channels do not return tokens under stream, which may result in incorrect token calculations.", "预计费选项": "Estimated fee options", "默认 API 版本": "Default API version", "默认 zh-CN-XiaochenNeural": "Default zh-C<PERSON>-XiaochenNeural", "默认 zh-CN-XiaohanNeural": "Default zh-C<PERSON>-XiaohanNeural", "默认 zh-CN-YunxiNeural": "Default zh-CN-YunxiNeural", "默认 zh-CN-YunxiNeural|boy": "Default zh-CN-YunxiNeural|boy", "默认 zh-CN-YunyangNeural": "Default zh-CN-YunyangNeural", "默认 zh-CN-YunyeNeural": "Default zh-CN-YunyeNeural", "plauground": "", "请输入版本号，例如：v3[1]": "Please enter the version number, for example: v3.1", "modelOwnedby": {"action": "Operation", "create": "New model ownership", "icon": "Icon", "iconTip": "Icons are used to display in the model list.", "id": "ID", "idTip": "Channel ID, a number. Please set it to a number greater than 1000; once set, it cannot be changed.", "name": "Name", "nameTip": "Channel Name", "title": "Model Ownership"}, "price": "Price", "模型映射关系：例如用户请求A模型，实际转发给渠道的模型为B。在B模型加前缀+，表示使用传入模型计费，例如：+gpt-3": {"5-turbo": "Model mapping relationship: For example, when a user requests Model A, the actual model forwarded to the channel is Model B. Adding a prefix \"+\" to Model B indicates using the incoming model for billing purposes, for example: +gpt-3.5-turbo"}, "单独设置代理地址，支持http和socks5，例如：http://127[0][0]": {"1:1080,代理地址中可以通过 `%s` 作为会话标识占位符，程序中检测到有占位符会根据Key生成唯一会话标识符进行替换": "Set the proxy address separately, supporting both http and socks5, for example: http://127.0.0.1:1080. You can use `%s` as a session identifier placeholder in the proxy address. When a placeholder is detected in the program, it will be replaced with a unique session identifier generated based on the Key."}, "请输入版本号，例如：v3[__i18n_ally_root__]": {"1": "", "__i18n_ally_root__": {"1": ""}}, "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3": {"__i18n_ally_root__": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": ""}}}, "__i18n_ally_root__": {"__i18n_ally_root__": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": ""}}}, "__i18n_ally_root__": {"__i18n_ally_root__": {"5*，表示支持所有gpt-3": {"5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3": {"5*是正确的，*gpt-3": {"5是错误的": ""}}}}}}}}}, "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx": {"__i18n_ally_root__": {"com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "", "__i18n_ally_root__": {"__i18n_ally_root__": {"com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "", "__i18n_ally_root__": {"__i18n_ally_root__": {"com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": ""}}}}}}, "用于测试使用的模型，为空时无法测速,如：gpt-3": {"__i18n_ally_root__": {"5-turbo，仅支持chat模型": ""}}, "禁用流式的模型": "Disable the streaming model", "请参考wiki中的文档获取key": {"__i18n_ally_root__": {" https://github": {"com/MartialBE/one-hub/wiki/VertexAI": ""}}}, "请输入你部署的Ollama地址，例如：http://127[__i18n_ally_root__]": {"0": {"0": {"1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": ""}}}, "这里填写禁用流式的模型，注意：如果填写了禁用流式的模型，那么这些模型在流式请求时会跳过该渠道": "Fill in here to disable the streaming model. Note: If you fill in to disable the streaming model, these models will be skipped for streaming requests on that channel."}