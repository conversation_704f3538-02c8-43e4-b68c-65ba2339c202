package gemini

import (
	"bytes"
	"done-hub/common/logger"
	"done-hub/common/requester"
	"done-hub/model"
	"done-hub/providers/base"
	"done-hub/providers/openai"
	"done-hub/types"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

type GeminiProviderFactory struct{}

// 创建 GeminiProvider
func (f GeminiProviderFactory) Create(channel *model.Channel) base.ProviderInterface {
	useOpenaiAPI := false
	useCodeExecution := false

	if channel.Plugin != nil {
		plugin := channel.Plugin.Data()
		if pWeb, ok := plugin["code_execution"]; ok {
			if enable, ok := pWeb["enable"].(bool); ok && enable {
				useCodeExecution = true
			}
		}

		if pWeb, ok := plugin["use_openai_api"]; ok {
			if enable, ok := pWeb["enable"].(bool); ok && enable {
				useOpenaiAPI = true
			}
		}
	}

	version := "v1beta"
	if channel.Other != "" {
		version = channel.Other
	}

	return &GeminiProvider{
		OpenAIProvider: openai.OpenAIProvider{
			BaseProvider: base.BaseProvider{
				Config:    getConfig(version),
				Channel:   channel,
				Requester: requester.NewHTTPRequester(*channel.Proxy, RequestErrorHandle(channel.Key)),
			},
			SupportStreamOptions: true,
		},
		UseOpenaiAPI:     useOpenaiAPI,
		UseCodeExecution: useCodeExecution,
	}
}

type GeminiProvider struct {
	openai.OpenAIProvider
	UseOpenaiAPI     bool
	UseCodeExecution bool
}

func getConfig(version string) base.ProviderConfig {
	return base.ProviderConfig{
		BaseURL:           "https://generativelanguage.googleapis.com",
		ChatCompletions:   fmt.Sprintf("/%s/chat/completions", version),
		ModelList:         "/models",
		ImagesGenerations: "1",
	}
}

// 请求错误处理
func RequestErrorHandle(key string) requester.HttpErrorHandler {
	return func(resp *http.Response) *types.OpenAIError {
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			logger.SysError(fmt.Sprintf("Failed to read Gemini error response body: %v", err))
			return nil
		}
		resp.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		// 记录原始错误响应用于调试
		responseStr := string(bodyBytes)
		if len(responseStr) > 2000 {
			responseStr = responseStr[:2000] + "...[truncated]"
		}
		logger.SysError(fmt.Sprintf("Gemini API error response (Status: %d): %s", resp.StatusCode, responseStr))

		geminiError := &GeminiErrorResponse{}
		if err := json.NewDecoder(resp.Body).Decode(geminiError); err == nil {
			logger.SysError(fmt.Sprintf("Parsed Gemini error - Code: %d, Status: %s, Message: %s",
				geminiError.ErrorInfo.Code, geminiError.ErrorInfo.Status, geminiError.ErrorInfo.Message))
			return errorHandle(geminiError, key)
		} else {
			geminiErrors := &GeminiErrors{}
			if err := json.Unmarshal(bodyBytes, geminiErrors); err == nil {
				logger.SysError(fmt.Sprintf("Parsed Gemini errors - Code: %d, Status: %s, Message: %s",
					geminiErrors.Error().ErrorInfo.Code, geminiErrors.Error().ErrorInfo.Status, geminiErrors.Error().ErrorInfo.Message))
				return errorHandle(geminiErrors.Error(), key)
			}
		}

		// 如果无法解析错误格式，记录原始响应
		logger.SysError(fmt.Sprintf("Unable to parse Gemini error response format, raw response: %s", responseStr))
		return nil
	}
}

// 错误处理
func errorHandle(geminiError *GeminiErrorResponse, key string) *types.OpenAIError {
	if geminiError.ErrorInfo == nil || geminiError.ErrorInfo.Message == "" {
		return nil
	}

	cleaningError(geminiError.ErrorInfo, key)

	return &types.OpenAIError{
		Message: geminiError.ErrorInfo.Message,
		Type:    "gemini_error",
		Param:   geminiError.ErrorInfo.Status,
		Code:    geminiError.ErrorInfo.Code,
	}
}

func cleaningError(errorInfo *GeminiError, key string) {
	if key == "" {
		return
	}
	message := strings.Replace(errorInfo.Message, key, "xxxxx", 1)
	errorInfo.Message = message
}

func (p *GeminiProvider) GetFullRequestURL(requestURL string, modelName string) string {
	baseURL := strings.TrimSuffix(p.GetBaseURL(), "/")
	version := "v1beta"

	if p.Channel.Other != "" {
		version = p.Channel.Other
	}

	inputVersion := p.Context.Param("version")
	if inputVersion != "" {
		version = inputVersion
	}

	return fmt.Sprintf("%s/%s/models/%s:%s", baseURL, version, modelName, requestURL)

}

// 获取请求头
func (p *GeminiProvider) GetRequestHeaders() (headers map[string]string) {
	headers = make(map[string]string)
	p.CommonRequestHeaders(headers)
	headers["x-goog-api-key"] = p.Channel.Key

	return headers
}
