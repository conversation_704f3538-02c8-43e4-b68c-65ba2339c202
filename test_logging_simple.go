package main

import (
	"context"
	"done-hub/common/logger"
	"done-hub/providers/gemini"
	"fmt"
	"net/http"
	"strings"
)

func main() {
	// 初始化日志系统
	logger.SetupLogger()

	// 测试日志功能
	ctx := context.WithValue(context.Background(), logger.RequestIdKey, "test-request-123")

	// 测试基本日志记录
	logger.LogInfo(ctx, "Testing Gemini logging functionality")
	logger.LogDebug(ctx, "This is a debug message for Gemini request")
	logger.LogError(ctx, "This is an error message for testing")

	// 测试 Gemini 错误处理
	testGeminiErrorHandling()

	fmt.Println("Logging test completed. Check the logs directory for output.")
}

func testGeminiErrorHandling() {
	// 模拟一个 Gemini 错误响应
	errorResponse := `{
		"error": {
			"code": 400,
			"message": "Request contains an invalid argument.",
			"status": "INVALID_ARGUMENT"
		}
	}`

	// 创建模拟的 HTTP 响应
	resp := &http.Response{
		StatusCode: 400,
		Header:     make(http.Header),
		Body:       http.NoBody,
	}
	resp.Header.Set("Content-Type", "application/json")
	resp.Body = &mockReadCloser{strings.NewReader(errorResponse)}

	// 测试错误处理函数
	errorHandler := gemini.RequestErrorHandle("test-key")
	openAIError := errorHandler(resp)

	if openAIError != nil {
		fmt.Printf("Parsed error: Code=%s, Message=%s, Type=%s\n", 
			openAIError.Code, openAIError.Message, openAIError.Type)
	}
}

// 模拟 ReadCloser
type mockReadCloser struct {
	*strings.Reader
}

func (m *mockReadCloser) Close() error {
	return nil
}
