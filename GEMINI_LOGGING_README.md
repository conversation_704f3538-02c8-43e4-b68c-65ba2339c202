# Gemini 详细日志功能说明

## 概述

为了帮助调试 Gemini/Vertex AI 请求中的 "Request contains an invalid argument" 错误，我们添加了详细的日志记录功能。这些日志将帮助你查看：

1. 前端发送的原始请求内容
2. 发送给上游 API 的具体请求
3. 上游 API 返回的详细错误信息
4. 请求处理过程中的各个步骤

## 启用详细日志

### 1. 设置日志级别

在配置文件中设置日志级别为 `debug`：

```yaml
log_level: "debug"
```

### 2. 重启服务

修改配置后重启 done-hub 服务以使配置生效。

## 日志内容说明

### 请求入口日志

在 `/gemini` 路径的请求处理入口，会记录：

```
[DEBUG] Gemini request - URL: /gemini/v1/models/gemini-pro:generateContent, Method: POST, ModelAction: gemini-pro:generateContent, Action: generateContent, IsStream: false
[DEBUG] Parsed Gemini request: {"contents":[...],"generationConfig":{...}}
```

### 上游请求日志

发送给 Google API 之前，会记录：

```
[DEBUG] Gemini upstream request - URL: https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent, Headers: map[...], Body: {...}
```

### HTTP 请求/响应日志

在 HTTP 层面会记录：

```
[DEBUG] HTTP Request - URL: https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent, Method: POST, Headers: map[...], Body: {...}
[DEBUG] HTTP Response - Status: 400, Headers: map[...]
```

### 错误响应日志

当出现错误时，会记录详细的错误信息：

```
[ERROR] Gemini API error response (Status: 400): {"error":{"code":400,"message":"Request contains an invalid argument.","status":"INVALID_ARGUMENT"}}
[ERROR] Parsed Gemini error - Code: 400, Status: INVALID_ARGUMENT, Message: Request contains an invalid argument.
[ERROR] HTTP Error Response - URL: https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent, Status: 400, Body: {...}
```

### 重试日志

当发生重试时，会记录：

```
[ERROR] relay error (channel #123(channel-name)): status_code=400, type=gemini_error, code=400, message=Request contains an invalid argument.
[ERROR] using channel #124(backup-channel) to retry (remain times 2)
```

## 查看日志

### 1. 控制台输出

启动服务时，详细的调试信息会直接输出到控制台。

### 2. 日志文件

日志也会写入到配置的日志文件中（默认 `./logs/done-hub.log`）。

### 3. 实时查看

可以使用 `tail` 命令实时查看日志：

```bash
tail -f ./logs/done-hub.log | grep -E "(DEBUG|ERROR).*Gemini"
```

## 测试日志功能

使用提供的测试脚本：

```bash
chmod +x test_gemini_logging.sh
./test_gemini_logging.sh
```

## 日志内容脱敏

为了保护敏感信息，日志中的内容会进行适当的脱敏处理：

1. 请求体内容超过 500-1000 字符时会被截断
2. API 密钥等敏感信息会被替换为 `xxxxx`
3. 响应内容超过 2000 字符时会被截断

## 故障排查步骤

当遇到 "Request contains an invalid argument" 错误时：

1. **检查请求格式**：查看 `Parsed Gemini request` 日志，确认请求格式是否正确
2. **检查上游请求**：查看 `Gemini upstream request` 日志，确认发送给 Google API 的请求是否正确
3. **检查错误详情**：查看 `Gemini API error response` 日志，获取具体的错误信息
4. **检查参数映射**：对比前端请求和上游请求，查看参数转换是否正确

## 注意事项

1. 详细日志会增加日志文件大小，建议在生产环境中谨慎使用
2. 调试完成后，建议将日志级别改回 `info` 或 `warn`
3. 定期清理日志文件以避免磁盘空间不足
