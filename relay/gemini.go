package relay

import (
	"done-hub/common"
	"done-hub/common/config"
	"done-hub/common/logger"
	"done-hub/common/requester"
	"done-hub/providers/gemini"
	"done-hub/safty"
	"done-hub/types"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

var AllowGeminiChannelType = []int{config.ChannelTypeGemini, config.ChannelTypeVertexAI}

type relayGeminiOnly struct {
	relayBase
	geminiRequest *gemini.GeminiChatRequest
}

func NewRelayGeminiOnly(c *gin.Context) *relayGeminiOnly {
	c.Set("allow_channel_type", AllowGeminiChannelType)
	relay := &relayGeminiOnly{
		relayBase: relayBase{
			allowHeartbeat: true,
			c:              c,
		},
	}

	return relay
}

func (r *relayGeminiOnly) setRequest() error {
	// 支持两种格式: /:version/models/:model 和 /:version/models/*action
	modelAction := r.c.Param("model")
	if modelAction == "" {
		// 尝试获取action参数（用于 model:predict 格式）
		actionPath := r.c.Param("action")
		if actionPath == "" {
			return errors.New("model is required")
		}
		// 去掉开头的斜杠
		actionPath = strings.TrimPrefix(actionPath, "/")
		modelAction = actionPath
	}

	modelList := strings.Split(modelAction, ":")
	if len(modelList) != 2 {
		return errors.New("model error")
	}

	isStream := false
	action := modelList[1]
	if action == "streamGenerateContent" {
		isStream = true
	}

	// 记录原始请求信息用于调试
	logger.LogDebug(r.c.Request.Context(), fmt.Sprintf("Gemini request - URL: %s, Method: %s, ModelAction: %s, Action: %s, IsStream: %t",
		r.c.Request.URL.String(), r.c.Request.Method, modelAction, action, isStream))

	r.geminiRequest = &gemini.GeminiChatRequest{}
	if err := common.UnmarshalBodyReusable(r.c, r.geminiRequest); err != nil {
		logger.LogError(r.c.Request.Context(), fmt.Sprintf("Failed to unmarshal Gemini request body: %v", err))
		return err
	}

	// 记录解析后的请求内容（脱敏处理）
	if requestBytes, err := json.Marshal(r.geminiRequest); err == nil {
		requestStr := string(requestBytes)
		// 简单脱敏：如果内容太长，只显示前500字符
		if len(requestStr) > 500 {
			requestStr = requestStr[:500] + "...[truncated]"
		}
		logger.LogDebug(r.c.Request.Context(), fmt.Sprintf("Parsed Gemini request: %s", requestStr))
	}
	r.geminiRequest.Model = modelList[0]
	r.geminiRequest.Stream = isStream
	r.setOriginalModel(r.geminiRequest.Model)
	// 设置原始模型到 Context，用于统一请求响应模型功能
	r.c.Set("original_model", r.geminiRequest.Model)

	return nil
}

func (r *relayGeminiOnly) getRequest() interface{} {
	return r.geminiRequest
}

func (r *relayGeminiOnly) IsStream() bool {
	return r.geminiRequest.Stream
}

func (r *relayGeminiOnly) getPromptTokens() (int, error) {
	channel := r.provider.GetChannel()
	return CountGeminiTokenMessages(r.geminiRequest, channel.PreCost)
}

func (r *relayGeminiOnly) send() (err *types.OpenAIErrorWithStatusCode, done bool) {
	chatProvider, ok := r.provider.(gemini.GeminiChatInterface)
	if !ok {
		return nil, false
	}

	// 内容审查
	if config.EnableSafe {
		for _, message := range r.geminiRequest.Contents {
			if message.Parts != nil {
				CheckResult, _ := safty.CheckContent(message.Parts)
				if !CheckResult.IsSafe {
					err = common.StringErrorWrapperLocal(CheckResult.Reason, CheckResult.Code, http.StatusBadRequest)
					done = true
					return
				}
			}
		}
	}

	r.geminiRequest.Model = r.modelName

	if r.geminiRequest.Stream {
		var response requester.StreamReaderInterface[string]
		response, err = chatProvider.CreateGeminiChatStream(r.geminiRequest)
		if err != nil {
			return
		}

		if r.heartbeat != nil {
			r.heartbeat.Stop()
		}

		doneStr := func() string {
			return ""
		}
		firstResponseTime := responseGeneralStreamClient(r.c, response, doneStr)
		r.SetFirstResponseTime(firstResponseTime)
	} else {
		var response *gemini.GeminiChatResponse
		response, err = chatProvider.CreateGeminiChat(r.geminiRequest)
		if err != nil {
			return
		}

		if r.heartbeat != nil {
			r.heartbeat.Stop()
		}

		err = responseJsonClient(r.c, response)
	}

	if err != nil {
		done = true
	}

	return
}

func (r *relayGeminiOnly) GetError(err *types.OpenAIErrorWithStatusCode) (int, any) {
	newErr := FilterOpenAIErr(r.c, err)

	geminiErr := gemini.OpenaiErrToGeminiErr(&newErr)

	return newErr.StatusCode, geminiErr.GeminiErrorResponse
}

func (r *relayGeminiOnly) HandleJsonError(err *types.OpenAIErrorWithStatusCode) {
	statusCode, response := r.GetError(err)
	r.c.JSON(statusCode, response)
}

func (r *relayGeminiOnly) HandleStreamError(err *types.OpenAIErrorWithStatusCode) {
	_, response := r.GetError(err)

	str, jsonErr := json.Marshal(response)
	if jsonErr != nil {
		return
	}
	r.c.Writer.Write([]byte("data: " + string(str) + "\n\n"))
	r.c.Writer.Flush()
}

func CountGeminiTokenMessages(request *gemini.GeminiChatRequest, preCostType int) (int, error) {
	if preCostType == config.PreContNotAll {
		return 0, nil
	}

	tokenEncoder := common.GetTokenEncoder(request.Model)

	tokenNum := 0
	tokensPerMessage := 4
	var textMsg strings.Builder

	for _, message := range request.Contents {
		tokenNum += tokensPerMessage
		for _, part := range message.Parts {
			if part.Text != "" {
				textMsg.WriteString(part.Text)
			}

			if part.InlineData != nil {
				// 其他类型的，暂时按200个token计算
				tokenNum += 200
			}
		}
	}

	if textMsg.Len() > 0 {
		tokenNum += common.GetTokenNum(tokenEncoder, textMsg.String())
	}
	return tokenNum, nil
}
